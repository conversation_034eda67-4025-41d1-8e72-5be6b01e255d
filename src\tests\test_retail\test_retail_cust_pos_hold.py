"""
客户当前持仓信息类的测试用例
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import pytest
from pydantic import ValidationError
from models.retail import CustPositionHolding


class TestCustPositionHolding:
    """客户当前持仓信息测试类"""
    
    def test_valid_position_holding_creation(self):
        """测试创建有效的客户持仓信息"""
        position_holding = CustPositionHolding(
            hbone_no="123456789",
            holding_bonds_domestic_proportion=0.30,
            holding_bonds_overseas_proportion=0.10,
            holding_cash_amount=50000.0,
            holding_cash_proportion=0.05,
            holding_commodity_proportion=0.15,
            holding_market_cap=1000000.0,
            holding_stocks_domestic_proportion=0.25,
            holding_stocks_overseas_proportion=0.20,
            recommend_bonds_domestic_proportion=0.35,
            recommend_bonds_overseas_proportion=0.15,
            recommend_commodity_proportion=0.10,
            recommend_stocks_domestic_proportion=0.25,
            recommend_stocks_overseas_proportion=0.15
        )
        
        assert position_holding.hbone_no == "123456789"
        assert position_holding.holding_market_cap == 1000000.0
        assert position_holding.holding_cash_amount == 50000.0

    
    def test_hbone_no_validation_empty(self):
        """测试客户一账通号为空时的验证错误"""
        with pytest.raises(ValidationError) as exc_info:
            CustPositionHolding(
                hbone_no="",  # 空账通号
                holding_bonds_domestic_proportion=0.30,
                holding_bonds_overseas_proportion=0.10,
                holding_cash_amount=50000.0,
                holding_cash_proportion=0.05,
                holding_commodity_proportion=0.15,
                holding_market_cap=1000000.0,
                holding_stocks_domestic_proportion=0.25,
                holding_stocks_overseas_proportion=0.20,
                recommend_bonds_domestic_proportion=0.35,
                recommend_bonds_overseas_proportion=0.15,
                recommend_commodity_proportion=0.10,
                recommend_stocks_domestic_proportion=0.25,
                recommend_stocks_overseas_proportion=0.15
            )
        
        assert "客户一账通号不能为空" in str(exc_info.value)
    
    def test_hbone_no_validation_whitespace(self):
        """测试客户一账通号只有空格时的验证错误"""
        with pytest.raises(ValidationError) as exc_info:
            CustPositionHolding(
                hbone_no="   ",  # 只有空格
                holding_bonds_domestic_proportion=0.30,
                holding_bonds_overseas_proportion=0.10,
                holding_cash_amount=50000.0,
                holding_cash_proportion=0.05,
                holding_commodity_proportion=0.15,
                holding_market_cap=1000000.0,
                holding_stocks_domestic_proportion=0.25,
                holding_stocks_overseas_proportion=0.20,
                recommend_bonds_domestic_proportion=0.35,
                recommend_bonds_overseas_proportion=0.15,
                recommend_commodity_proportion=0.10,
                recommend_stocks_domestic_proportion=0.25,
                recommend_stocks_overseas_proportion=0.15
            )
        
        assert "客户一账通号不能为空" in str(exc_info.value)
    
    def test_holding_proportions_exceed_limit(self):
        """测试持仓占比总和超过1.0时的验证错误"""
        with pytest.raises(ValidationError) as exc_info:
            CustPositionHolding(
                hbone_no="123456789",
                holding_bonds_domestic_proportion=0.40,
                holding_bonds_overseas_proportion=0.30,
                holding_cash_amount=50000.0,
                holding_cash_proportion=0.05,
                holding_commodity_proportion=0.20,
                holding_market_cap=1000000.0,
                holding_stocks_domestic_proportion=0.30,
                holding_stocks_overseas_proportion=0.25,  # 总和1.45，超过1.0
                recommend_bonds_domestic_proportion=0.35,
                recommend_bonds_overseas_proportion=0.15,
                recommend_commodity_proportion=0.10,
                recommend_stocks_domestic_proportion=0.25,
                recommend_stocks_overseas_proportion=0.15
            )
        
        assert "当前持仓各类资产占比总和不能超过1.0" in str(exc_info.value)
    
    def test_recommend_proportions_exceed_limit(self):
        """测试推荐占比总和超过1.0时的验证错误"""
        with pytest.raises(ValidationError) as exc_info:
            CustPositionHolding(
                hbone_no="123456789",
                holding_bonds_domestic_proportion=0.30,
                holding_bonds_overseas_proportion=0.10,
                holding_cash_amount=50000.0,
                holding_cash_proportion=0.05,
                holding_commodity_proportion=0.15,
                holding_market_cap=1000000.0,
                holding_stocks_domestic_proportion=0.25,
                holding_stocks_overseas_proportion=0.20,
                recommend_bonds_domestic_proportion=0.40,
                recommend_bonds_overseas_proportion=0.30,
                recommend_commodity_proportion=0.20,
                recommend_stocks_domestic_proportion=0.30,
                recommend_stocks_overseas_proportion=0.25  # 总和1.45，超过1.0
            )
        
        assert "推荐配置各类资产占比总和不能超过1.0" in str(exc_info.value)
    
    def test_negative_proportion_validation(self):
        """测试负数占比的验证错误"""
        with pytest.raises(ValidationError):
            CustPositionHolding(
                hbone_no="123456789",
                holding_bonds_domestic_proportion=-0.1,  # 负数占比
                holding_bonds_overseas_proportion=0.10,
                holding_cash_amount=50000.0,
                holding_cash_proportion=0.05,
                holding_commodity_proportion=0.15,
                holding_market_cap=1000000.0,
                holding_stocks_domestic_proportion=0.25,
                holding_stocks_overseas_proportion=0.20,
                recommend_bonds_domestic_proportion=0.35,
                recommend_bonds_overseas_proportion=0.15,
                recommend_commodity_proportion=0.10,
                recommend_stocks_domestic_proportion=0.25,
                recommend_stocks_overseas_proportion=0.15
            )
    
    def test_proportion_exceeds_one(self):
        """测试单个占比超过1.0时的验证错误"""
        with pytest.raises(ValidationError):
            CustPositionHolding(
                hbone_no="123456789",
                holding_bonds_domestic_proportion=1.5,  # 超过1.0
                holding_bonds_overseas_proportion=0.10,
                holding_cash_amount=50000.0,
                holding_cash_proportion=0.05,
                holding_commodity_proportion=0.15,
                holding_market_cap=1000000.0,
                holding_stocks_domestic_proportion=0.25,
                holding_stocks_overseas_proportion=0.20,
                recommend_bonds_domestic_proportion=0.35,
                recommend_bonds_overseas_proportion=0.15,
                recommend_commodity_proportion=0.10,
                recommend_stocks_domestic_proportion=0.25,
                recommend_stocks_overseas_proportion=0.15
            )
    
    def test_negative_amount_validation(self):
        """测试负数金额的验证错误"""
        with pytest.raises(ValidationError):
            CustPositionHolding(
                hbone_no="123456789",
                holding_bonds_domestic_proportion=0.30,
                holding_bonds_overseas_proportion=0.10,
                holding_cash_amount=-1000.0,  # 负数现金
                holding_cash_proportion=0.001,
                holding_commodity_proportion=0.15,
                holding_market_cap=1000000.0,
                holding_stocks_domestic_proportion=0.25,
                holding_stocks_overseas_proportion=0.20,
                recommend_bonds_domestic_proportion=0.35,
                recommend_bonds_overseas_proportion=0.15,
                recommend_commodity_proportion=0.10,
                recommend_stocks_domestic_proportion=0.25,
                recommend_stocks_overseas_proportion=0.15
            )
    
    
    def test_boundary_values(self):
        """测试边界值"""
        # 测试所有占比为0的情况
        position_holding_zero = CustPositionHolding(
            hbone_no="123456789",
            holding_bonds_domestic_proportion=0.0,
            holding_bonds_overseas_proportion=0.0,
            holding_cash_amount=0.0,
            holding_cash_proportion=0.0,
            holding_commodity_proportion=0.0,
            holding_market_cap=0.0,
            holding_stocks_domestic_proportion=0.0,
            holding_stocks_overseas_proportion=0.0,
            recommend_bonds_domestic_proportion=0.0,
            recommend_bonds_overseas_proportion=0.0,
            recommend_commodity_proportion=0.0,
            recommend_stocks_domestic_proportion=0.0,
            recommend_stocks_overseas_proportion=0.0
        )
        assert position_holding_zero.hbone_no == "123456789"
        
        # 测试占比接近1.0的情况
        position_holding_max = CustPositionHolding(
            hbone_no="123456789",
            holding_bonds_domestic_proportion=0.999,
            holding_bonds_overseas_proportion=0.0,
            holding_cash_amount=999999.0,
            holding_cash_proportion=0.001,
            holding_commodity_proportion=0.0,
            holding_market_cap=999999999.0,
            holding_stocks_domestic_proportion=0.0,
            holding_stocks_overseas_proportion=0.0,
            recommend_bonds_domestic_proportion=0.0,
            recommend_bonds_overseas_proportion=0.0,
            recommend_commodity_proportion=0.0,
            recommend_stocks_domestic_proportion=0.999,
            recommend_stocks_overseas_proportion=0.0
        )
        assert position_holding_max.holding_bonds_domestic_proportion == 0.999


if __name__ == "__main__":
    # 运行简单的测试示例
    try:
        # 创建一个有效的客户持仓信息实例
        position_holding = CustPositionHolding(
            hbone_no="  123456789  ",  # 测试空格处理
            holding_bonds_domestic_proportion=0.30,
            holding_bonds_overseas_proportion=0.10,
            holding_cash_amount=50000.0,
            holding_cash_proportion=0.05,
            holding_commodity_proportion=0.10,
            holding_market_cap=1000000.0,
            holding_stocks_domestic_proportion=0.25,
            holding_stocks_overseas_proportion=0.20,  # 持仓总和1.0
            recommend_bonds_domestic_proportion=0.35,
            recommend_bonds_overseas_proportion=0.15,
            recommend_commodity_proportion=0.10,
            recommend_stocks_domestic_proportion=0.25,
            recommend_stocks_overseas_proportion=0.15  # 推荐总和1.0
        )
        
        print("✅ 客户持仓信息创建成功!")
        print(f"客户一账通号: {position_holding.hbone_no}")
        print(f"持仓市值: {position_holding.holding_market_cap}")
        print(f"持有现金: {position_holding.holding_cash_amount}")
        print(f"持有国内股票占比: {position_holding.holding_stocks_domestic_proportion}")
        print(f"持有海外股票占比: {position_holding.holding_stocks_overseas_proportion}")
        print(f"持有国内债券占比: {position_holding.holding_bonds_domestic_proportion}")
        print(f"持有海外债券占比: {position_holding.holding_bonds_overseas_proportion}")
        print(f"持有商品占比: {position_holding.holding_commodity_proportion}")
        print(f"推荐国内股票占比: {position_holding.recommend_stocks_domestic_proportion}")
        print(f"推荐海外股票占比: {position_holding.recommend_stocks_overseas_proportion}")
        print(f"推荐国内债券占比: {position_holding.recommend_bonds_domestic_proportion}")
        print(f"推荐有海外债券占比: {position_holding.recommend_bonds_overseas_proportion}")
        print(f"推荐商品占比: {position_holding.recommend_commodity_proportion}")
        
    except ValidationError as e:
        print(f"❌ 验证错误: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")
