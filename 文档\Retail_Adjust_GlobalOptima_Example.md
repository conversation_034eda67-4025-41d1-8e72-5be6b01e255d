## 1. 接口请求示例
curl -X 'POST' \
  'http://192.168.211.119:8000/api/v1/retail/adjust/global-optima' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
	"request_id": "cd03386e-e02a-4998-8885-f81c38ed593f",
	"adjust_solutions": [{
			"hbone_no": "80000001",
			"fund_num": 3,
			"strategy_priority_min": 1,
			"strategy_priority_sum": 7,
			"product_priority_min": 1,
			"product_priority_sum": 4,
			"deviation_avg": 0.029479879999999997,
			"adjust_solution": [{
					"product_code": "P00003",
					"adjust_amount": 56100,
					"product_type": "zh"
				},
				{
					"product_code": "P00005",
					"adjust_amount": 181333,
					"product_type": "zh"
				},
				{
					"product_code": "P00011",
					"adjust_amount": 245667,
					"product_type": "zh"
				}
			]
		},
		{
			"hbone_no": "80000001",
			"fund_num": 3,
			"strategy_priority_min": 2,
			"strategy_priority_sum": 8,
			"product_priority_min": 1,
			"product_priority_sum": 4,
			"deviation_avg": 0.029479879999999997,
			"adjust_solution": [{
					"product_code": "P00004",
					"adjust_amount": 56100,
					"product_type": "zh"
				},
				{
					"product_code": "P00005",
					"adjust_amount": 181333,
					"product_type": "zh"
				},
				{
					"product_code": "P00011",
					"adjust_amount": 245667,
					"product_type": "zh"
				}
			]
		}
	]
}'

## 2. 接口返回示例
{
  "code": 0,
  "message": "success",
  "data": {
    "hbone_no": "80000001",
    "fund_num": 3,
    "strategy_priority_min": 1,
    "strategy_priority_sum": 7,
    "product_priority_min": 1,
    "product_priority_sum": 4,
    "deviation_avg": 0.029479879999999997,
    "adjust_solution": [
      {
        "product_code": "P00003",
        "product_type": "zh",
        "adjust_amount": 56100
      },
      {
        "product_code": "P00005",
        "product_type": "zh",
        "adjust_amount": 181333
      },
      {
        "product_code": "P00011",
        "product_type": "zh",
        "adjust_amount": 245667
      }
    ]
  },
  "cost_time": 0.00518488883972168
}