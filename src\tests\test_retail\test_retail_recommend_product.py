"""
推荐产品池类的测试用例 (retail.py)
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import pytest
from pydantic import ValidationError
from models.retail import RecommendProduct, StrategyType


class TestRecommendProduct:
    """推荐产品池测试类"""
    
    def test_valid_product_pool_creation(self):
        """测试创建有效的推荐产品池"""
        product_pool = RecommendProduct(
            product_code="H04066",
            product_type="fund",
            stocks_domestic_proportion=0.25,
            stocks_overseas_proportion=0.15,
            bonds_domestic_proportion=0.30,
            bonds_overseas_proportion=0.10,
            commodity_proportion=0.15,  # 总和0.95，小于1.0
            product_priority='1',
            strategy_type=StrategyType.MULTI_STRATEGY,
            lower_amount_limit=1000.0,
            upper_amount_limit=10000000.0
        )
        
        assert product_pool.product_code == "H04066"
        assert product_pool.strategy_type == StrategyType.MULTI_STRATEGY
        assert product_pool.stocks_domestic_proportion == 0.25
        assert product_pool.stocks_overseas_proportion == 0.15
        assert product_pool.bonds_domestic_proportion == 0.30
        assert product_pool.bonds_overseas_proportion == 0.10
        assert product_pool.commodity_proportion == 0.15
        assert product_pool.lower_amount_limit == 1000.0
        assert product_pool.upper_amount_limit == 10000000.0
    
    def test_product_code_validation_empty(self):
        """测试基金代码为空时的验证错误"""
        with pytest.raises(ValidationError) as exc_info:
            RecommendProduct(
                product_code="",  # 空基金代码
                product_type="zh",
                stocks_domestic_proportion=0.25,
                stocks_overseas_proportion=0.15,
                bonds_domestic_proportion=0.30,
                bonds_overseas_proportion=0.10,
                commodity_proportion=0.15,
                product_priority='1',
                strategy_type=StrategyType.MULTI_STRATEGY,
                lower_amount_limit=1000.0,
                upper_amount_limit=10000000.0
            )
        
        assert "基金代码不能为空" in str(exc_info.value)
    
    def test_product_code_validation_whitespace(self):
        """测试基金代码只有空格时的验证错误"""
        with pytest.raises(ValidationError) as exc_info:
            RecommendProduct(
                product_code="   ",  # 只有空格的基金代码
                product_type="fund",
                stocks_domestic_proportion=0.25,
                stocks_overseas_proportion=0.15,
                bonds_domestic_proportion=0.30,
                bonds_overseas_proportion=0.10,
                commodity_proportion=0.15,
                product_priority='1',
                strategy_type=StrategyType.MULTI_STRATEGY,
                lower_amount_limit=1000.0,
                upper_amount_limit=10000000.0
            )
        
        assert "基金代码不能为空" in str(exc_info.value)
    
    
    def test_proportion_validation_exceeds_limit(self):
        """测试资产占比总和超过1.0时的验证错误"""
        with pytest.raises(ValidationError) as exc_info:
            RecommendProduct(
                product_code="H04066",
                product_type="zh",
                stocks_domestic_proportion=0.30,
                stocks_overseas_proportion=0.25,
                bonds_domestic_proportion=0.30,
                bonds_overseas_proportion=0.20,
                commodity_proportion=0.10,  # 总和为1.15，超过1.0
                product_priority='1',
                strategy_type=StrategyType.MULTI_STRATEGY,
                lower_amount_limit=1000.0,
                upper_amount_limit=10000000.0
            )
        
        assert "除去现金后，各类资产占比总和必须小于1.0" in str(exc_info.value)
    
    def test_proportion_validation_equals_one(self):
        """测试资产占比总和等于1.0的边界情况"""
        product_pool = RecommendProduct(
            product_code="H04066",
            product_type="fund",
            stocks_domestic_proportion=0.25,
            stocks_overseas_proportion=0.15,
            bonds_domestic_proportion=0.30,
            bonds_overseas_proportion=0.10,
            commodity_proportion=0.20,  # 总和正好为1.0
            product_priority='1',
            strategy_type=StrategyType.MULTI_STRATEGY,
            lower_amount_limit=1000.0,
            upper_amount_limit=10000000.0
        )
        
        # 应该能成功创建，因为等于1.0不超过限制
        assert product_pool.product_code == "H04066"
    
    def test_amount_limit_validation_error(self):
        """测试金额限制验证错误"""
        with pytest.raises(ValidationError) as exc_info:
            RecommendProduct(
                product_code="H04066",
                product_type="zh",
                stocks_domestic_proportion=0.25,
                stocks_overseas_proportion=0.15,
                bonds_domestic_proportion=0.30,
                bonds_overseas_proportion=0.10,
                commodity_proportion=0.15,
                product_priority='1',
                strategy_type=StrategyType.MULTI_STRATEGY,
                lower_amount_limit=10000000.0,  # 下限大于上限
                upper_amount_limit=1000.0
            )
        
        assert "单笔购买金额下限必须小于上限" in str(exc_info.value)
    
    def test_amount_limit_validation_equal(self):
        """测试金额限制相等时的验证错误"""
        with pytest.raises(ValidationError) as exc_info:
            RecommendProduct(
                product_code="H04066",
                product_type="fund",
                stocks_domestic_proportion=0.25,
                stocks_overseas_proportion=0.15,
                bonds_domestic_proportion=0.30,
                bonds_overseas_proportion=0.10,
                commodity_proportion=0.15,
                product_priority='1',
                strategy_type=StrategyType.MULTI_STRATEGY,
                lower_amount_limit=1000.0,  # 下限等于上限
                upper_amount_limit=1000.0
            )
        
        assert "单笔购买金额下限必须小于上限" in str(exc_info.value)
    
    def test_proportion_negative_value(self):
        """测试占比为负数时的验证错误"""
        with pytest.raises(ValidationError):
            RecommendProduct(
                product_code="H04066",
                product_type="zh",
                stocks_domestic_proportion=-0.1,  # 负数占比
                stocks_overseas_proportion=0.15,
                bonds_domestic_proportion=0.30,
                bonds_overseas_proportion=0.10,
                commodity_proportion=0.15,
                product_priority='1',
                strategy_type=StrategyType.MULTI_STRATEGY,
                lower_amount_limit=1000.0,
                upper_amount_limit=10000000.0
            )
    
    def test_proportion_exceeds_one(self):
        """测试单个占比超过1.0时的验证错误"""
        with pytest.raises(ValidationError):
            RecommendProduct(
                product_code="H04066",
                product_type="fund",
                stocks_domestic_proportion=1.5,  # 超过1.0的占比
                stocks_overseas_proportion=0.15,
                bonds_domestic_proportion=0.30,
                bonds_overseas_proportion=0.10,
                commodity_proportion=0.15,
                product_priority='1',
                strategy_type=StrategyType.MULTI_STRATEGY,
                lower_amount_limit=1000.0,
                upper_amount_limit=10000000.0
            )
    
    def test_negative_amount_limit(self):
        """测试负数金额限制的验证错误"""
        with pytest.raises(ValidationError):
            RecommendProduct(
                product_code="H04066",
                product_type="zh",
                stocks_domestic_proportion=0.25,
                stocks_overseas_proportion=0.15,
                bonds_domestic_proportion=0.30,
                bonds_overseas_proportion=0.10,
                commodity_proportion=0.15,
                product_priority='1',
                strategy_type=StrategyType.MULTI_STRATEGY,
                lower_amount_limit=-1000.0,  # 负数下限
                upper_amount_limit=10000000.0
            )
    
    def test_zero_upper_amount_limit(self):
        """测试上限为0时的验证错误"""
        with pytest.raises(ValidationError):
            RecommendProduct(
                product_code="H04066",
                product_type="fund",
                stocks_domestic_proportion=0.25,
                stocks_overseas_proportion=0.15,
                bonds_domestic_proportion=0.30,
                bonds_overseas_proportion=0.10,
                commodity_proportion=0.15,
                product_priority='1',
                strategy_type=StrategyType.MULTI_STRATEGY,
                lower_amount_limit=1000.0,
                upper_amount_limit=0.0  # 上限为0
            )
    
    def test_product_priority_enum_values(self):
        """测试产品优先级枚举值"""
        # 测试其他优先级
        product_pool_other = RecommendProduct(
            product_code="H04066",
            product_type="zh",
            stocks_domestic_proportion=0.25,
            stocks_overseas_proportion=0.15,
            bonds_domestic_proportion=0.30,
            bonds_overseas_proportion=0.10,
            commodity_proportion=0.15,
            product_priority='1',
            strategy_type=StrategyType.SINGLE_STRATEGY,
            lower_amount_limit=1000.0,
            upper_amount_limit=10000000.0
        )
        assert product_pool_other.product_priority.value == 0
        
        # 测试全球配置表优先级
        product_pool_global = RecommendProduct(
            product_code="H04066",
            product_type="fund",
            stocks_domestic_proportion=0.25,
            stocks_overseas_proportion=0.15,
            bonds_domestic_proportion=0.30,
            bonds_overseas_proportion=0.10,
            commodity_proportion=0.15,
            product_priority='1',
            strategy_type=StrategyType.DUAL_STRATEGY,
            lower_amount_limit=1000.0,
            upper_amount_limit=10000000.0
        )
        assert product_pool_global.product_priority.value == 2
    
    def test_strategy_type_enum_values(self):
        """测试策略类型枚举值"""
        # 测试单策略
        product_pool_single = RecommendProduct(
            product_code="H04066",
            product_type="zh",
            stocks_domestic_proportion=0.25,
            stocks_overseas_proportion=0.15,
            bonds_domestic_proportion=0.30,
            bonds_overseas_proportion=0.10,
            commodity_proportion=0.15,
            product_priority='1',
            strategy_type=StrategyType.SINGLE_STRATEGY,
            lower_amount_limit=1000.0,
            upper_amount_limit=10000000.0
        )
        assert product_pool_single.strategy_type == StrategyType.SINGLE_STRATEGY
        assert product_pool_single.strategy_type.value == 1
        
        # 测试双拼策略
        product_pool_dual = RecommendProduct(
            product_code="H04066",
            product_type="fund",
            stocks_domestic_proportion=0.25,
            stocks_overseas_proportion=0.15,
            bonds_domestic_proportion=0.30,
            bonds_overseas_proportion=0.10,
            commodity_proportion=0.15,
            product_priority='1',
            strategy_type=StrategyType.DUAL_STRATEGY,
            lower_amount_limit=1000.0,
            upper_amount_limit=10000000.0
        )
        assert product_pool_dual.strategy_type == StrategyType.DUAL_STRATEGY
        assert product_pool_dual.strategy_type.value == 2
    
    def test_boundary_values(self):
        """测试边界值"""
        # 测试所有占比为0的情况
        product_pool_zero = RecommendProduct(
            product_code="H04066",
            product_type="zh",
            stocks_domestic_proportion=0.0,
            stocks_overseas_proportion=0.0,
            bonds_domestic_proportion=0.0,
            bonds_overseas_proportion=0.0,
            commodity_proportion=0.0,
            product_priority='1',
            strategy_type=StrategyType.MULTI_STRATEGY,
            lower_amount_limit=0.0,  # 最小下限
            upper_amount_limit=1.0   # 最小上限
        )
        assert product_pool_zero.product_code == "H04066"
        
        # 测试占比接近1.0的情况
        product_pool_max = RecommendProduct(
            product_code="H04066",
            product_type="fund",
            stocks_domestic_proportion=0.999,
            stocks_overseas_proportion=0.0,
            bonds_domestic_proportion=0.0,
            bonds_overseas_proportion=0.0,
            commodity_proportion=0.0,
            product_priority='1',
            strategy_type=StrategyType.MULTI_STRATEGY,
            lower_amount_limit=1.0,
            upper_amount_limit=999999999.0
        )
        assert product_pool_max.stocks_domestic_proportion == 0.999


if __name__ == "__main__":
    # 运行简单的测试示例
    try:
        # 创建一个有效的产品池实例
        product_pool = RecommendProduct(
            product_code="h04066", 
            product_type= "zh",
            stocks_domestic_proportion=0.25,
            stocks_overseas_proportion=0.15,
            bonds_domestic_proportion=0.30,
            bonds_overseas_proportion=0.10,
            commodity_proportion=0.2, 
            product_priority='1',
            strategy_type="singleStra",
            lower_amount_limit=1000.0,
            upper_amount_limit=10000000.0
        )
        
        # assert product_pool.strategy_type == StrategyType.SINGLE_STRATEGY

        print("✅ 推荐产品池创建成功!")
        print(f"基金代码: {product_pool.product_code}")
        print(f"产品优先级: {product_pool.product_priority.name} (值: {product_pool.product_priority.value})")
        print(f"策略类型: {product_pool.strategy_type.name} (值: {product_pool.strategy_type.value})")
        print(f"国内股票占比: {product_pool.stocks_domestic_proportion}")
        print(f"海外股票占比: {product_pool.stocks_overseas_proportion}")
        print(f"国内债券占比: {product_pool.bonds_domestic_proportion}")
        print(f"海外债券占比: {product_pool.bonds_overseas_proportion}")
        print(f"商品占比: {product_pool.commodity_proportion}")
        print(f"总资产占比: {product_pool.stocks_domestic_proportion + product_pool.stocks_overseas_proportion + product_pool.bonds_domestic_proportion + product_pool.bonds_overseas_proportion + product_pool.commodity_proportion}")
        print(f"金额范围: {product_pool.lower_amount_limit} - {product_pool.upper_amount_limit}")
        
    except ValidationError as e:
        print(f"❌ 验证错误: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")
