
from pydantic import BaseModel, Field, field_validator, model_validator
from typing import Optional, Union, List, Set
from loguru import logger

_FIELD_GROUPS = {
    "gp": {"gp_gn", "gp_hw"},      # 国内/海外股票
    "gs": {"gs_gn", "gs_hw"},      # 国内/海外固收
    "cta": {"cta_gn", "cta_hw"},   # 国内/海外期货
    "ll": {"ll_gn", "ll_hw"}       # 国内/海外另类投资
}

class FofPortfolio(BaseModel):
    """
    FoF产品池
    """

    columns: List[str] = Field(
        description="data的列名",
        example=["index", "fund_code", "fof_type", "min_amt", "gp_gn", "gp_hw", "cta", "gs_gn", "gs_hw", "ll"]
    )

    data: List[List[str]] = Field(
        description="二维数据矩阵，列维度需与columns一致",
        example=[["0", "H04066", "海外多策略", "10", "0.000", "0.5490", "0.1469", "0", "0.0982", "0.2064"],
                 ["1", "SVX355", "单策略", "1000000", "1", "0.0000", "0.0000", "0.0000", "0.0000", "0.0000"],
                 ["2", "SNQ525", "国内多策略","1000000",  "0.1702", "0.0000", "0.2311",  "0.5164", "0.0000", "0.0783"],]
    )

    
    @field_validator("columns")
    def columns_validator(cls, value):
        valid_fields = {
            "index",        # 索引,
            "fund_code",    # 基金代码,
            "fof_type",     # 策略组合类型,
            "min_amt",      # 最小投资金额,
            "gp",           # 股票
            "gp_gn",        # 股票_国内
            "gp_hw",        # 股票_海外
            "cta",          # 期货
            "cta_gn",       # 期货_国内
            "cta_hw",       # 期货_海外
            "gs",           # 固收
            "gs_gn",        # 固收_国内
            "gs_hw",        # 固收_海外
            "ll",           # 另类
            "ll_gn",        # 另类_国内
            "ll_hw",        # 另类_海外
        }

        required_columns = ["index", "fund_code", "fof_type", "min_amt"]

        column_set = set(value)

        if len(value) != len(column_set):
            raise ValueError("columns 字段不能重复")
        
        if not column_set.issubset(valid_fields):
            raise ValueError(f'columns 必须为以下字段集合的子集: {valid_fields}')
        
        for column in required_columns:
            if column not in column_set:
                raise ValueError(f'columns 必须包含字段: "{column}"')

        for group_name in _FIELD_GROUPS.keys():
            # logger.debug(f'检查字段分组: {group_name}')
            check_fof_group(group_name, column_set)

        return value
    
            
    @model_validator(mode="after")
    def data_validator(cls, values):
        data_dims = [(i, len(e)) for i, e in enumerate(values.data) if len(e) != len(values.columns)]
        if data_dims:
            raise ValueError('data的第二维大小必须与columns一致')
        
        return values



class CustHoldRatio(BaseModel):
    """
    客户持仓策略分布
    """

    columns: List[str] = Field(
        description="data的列名",
        example=["market_cap", "gp_gn", "gp_hw", "cta", "gs_gn", "gs_hw", "ll"]
    )

    data: List[str] = Field(
        description="一维数据矩阵，需与columns一一对应",
        example=["20979383.31", "0.2421", "0.0", "0.3197", "0.0955", "0.0", "0.0"]
    )


    @field_validator("columns")
    def columns_validator(cls, value):
        valid_fields = {
            "market_cap",   # 持仓总市值
            "gp",           # 股票
            "gp_gn",        # 股票_国内
            "gp_hw",        # 股票_海外
            "cta",          # 期货
            "cta_gn",       # 期货_国内
            "cta_hw",       # 期货_海外
            "gs",           # 固收
            "gs_gn",        # 固收_国内
            "gs_hw",        # 固收_海外
            "ll",           # 另类
            "ll_gn",        # 另类_国内
            "ll_hw",        # 另类_海外
        }

        required_columns = ["market_cap"]

        column_set = set(value)

        if len(value) != len(column_set):
            raise ValueError("columns 字段不能重复")
        
        if not column_set.issubset(valid_fields):
            raise ValueError(f'columns 必须为以下字段集合的子集: {valid_fields}')
        
        for column in required_columns:
            if column not in column_set:
                raise ValueError(f'columns 必须包含字段: "{column}"')
            
        for group_name in _FIELD_GROUPS.keys():
            # logger.debug(f'检查字段分组: {group_name}')
            check_fof_group(group_name, column_set)
        
        return value
    

    @model_validator(mode="after")
    def data_validator(cls, values):
        if len(values.data) != len(values.columns):
            raise ValueError("data需与columns列表长度不一致")
        
        return values
        

class CustRecommendRatio(BaseModel):
    """
    客户的好买推荐配置分布
    """

    columns: List[str] = Field(
        description="data的列名",
        example=["gp_gn", "gp_hw", "cta", "gs_gn", "gs_hw", "ll"]
    )

    data: List[str] = Field(
        description="一维数据矩阵，需与columns一一对应",
        example=["0.25", "0.15", "0.1", "0.13", "0.07", "0.05"]
    )


    @field_validator("columns")
    def columns_validator(cls, value):
        valid_fields = {
            "gp",           # 股票
            "gp_gn",        # 股票_国内
            "gp_hw",        # 股票_海外
            "cta",          # 期货
            "cta_gn",       # 期货_国内
            "cta_hw",       # 期货_海外
            "gs",           # 固收
            "gs_gn",        # 固收_国内
            "gs_hw",        # 固收_海外
            "ll",           # 另类
            "ll_gn",        # 另类_国内
            "ll_hw",        # 另类_海外
        }

        column_set = set(value)

        if len(value) != len(column_set):
            raise ValueError("columns 字段不能重复")
        
        if not column_set.issubset(valid_fields):
            raise ValueError(f'columns 必须为以下字段集合的子集: {valid_fields}')
         
        for group_name in _FIELD_GROUPS.keys():
            # logger.debug(f'检查字段分组: {group_name}')
            check_fof_group(group_name, column_set)
        
        return value
    

    @model_validator(mode="after")
    def data_validator(cls, values):
        if len(values.data) != len(values.columns):
            raise ValueError("data需与columns列表长度不一致")
        
        return values
           
class FofLpProblem(BaseModel):
    """
    FOF调仓线性规划问题
    """
    fof_portfolio_index: List[str] = Field(description="FOF产品（下标）组合", example=['0', '3'])

    bias: float = Field(description="偏离度", example=0.05)
        

class CustOptimizeRatio(BaseModel):
    """
    客户优化配置分布
    """

    fof_portfolio: FofPortfolio
    cust_hold_ratio: CustHoldRatio
    cust_recommend_ratio: CustRecommendRatio
    lp_problem_list: List[FofLpProblem] = Field(
        description="FOF调仓线性规划问题列表",  
        example=[{"fof_portfolio_index": ["0", "1"], "bias": 0.05},
                 {"fof_portfolio_index": ["0", "2"], "bias": 0.05},
                 {"fof_portfolio_index": ["1", "2"], "bias": 0.05},
                 {"fof_portfolio_index": ["0", "1", "2"], "bias": 0.05},
                 ])



    @model_validator(mode="after")
    def data_validator(cls, values):
        fof_portfolio_cols = set(values.fof_portfolio.columns)
        cust_hold_ratio_cols = set(values.cust_hold_ratio.columns)
        cust_recommend_ratio_cols = set(values.cust_recommend_ratio.columns)

        all_strategy_columns = set()
        for group_name in _FIELD_GROUPS.keys():
            # logger.debug(f'检查字段分组: {group_name}')
            all_strategy_columns.add(group_name)
            all_strategy_columns |= _FIELD_GROUPS[group_name]

        # logger.debug(f'所有字段: {all_strategy_columns}')

        intersect_fof_col = fof_portfolio_cols & all_strategy_columns
        intersect_hold_col = cust_hold_ratio_cols & all_strategy_columns
        intersect_hold_col = cust_recommend_ratio_cols & all_strategy_columns

        if intersect_fof_col != intersect_hold_col or intersect_fof_col != intersect_hold_col or intersect_hold_col != intersect_hold_col:
            raise ValueError(f'fof_portfolio、cust_hold_ratio、cust_recommend_ratio 三者策略必须一致')
        
        # 检查线性问题中组合下标index是否都在fof_portfolio中
        col_idx = values.fof_portfolio.columns.index("index")
        base_index = set()
        for d in values.fof_portfolio.data:
            base_index.add(d[col_idx])
        
        check_index = set()
        for lp_problem in values.lp_problem_list:
            check_index |= set(lp_problem.fof_portfolio_index)
        
        if not check_index.issubset(base_index):
            raise ValueError(f'lp_problem.fof_portfolio_index下标不在fof_portfolio.index中')

        return values
        


def check_fof_group(group_name: str, column_set: Set[str]):
    # 预定义字段分组规则
    # logger.debug(f'传入值: {column_set}')
    group = _FIELD_GROUPS[group_name]
    # logger.debug(f'检查字段分组值: {group}')
    has_base = group_name in column_set
    has_subfields = group.issubset(column_set)

    if has_base and has_subfields:
        raise ValueError( f'不能同时包含 "{group_name}" 及 {group}')
    
    if not has_base and not has_subfields:
        raise ValueError(f'必须包含 "{group_name}" 或 {group}')
    
    if has_base:
        for subfield in group:
            if subfield in column_set:
                raise ValueError(f'只能包含 "{group_name}" 或 {group}')



