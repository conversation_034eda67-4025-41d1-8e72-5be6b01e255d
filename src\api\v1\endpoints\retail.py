from fastapi import APIRouter
from models.retail import CustAdjustPosition, CustLpOptimalSolutionList
from services.retail_fund_adjust import RetailFundAdjustService, getGlobalOptimalSolution
from loguru import logger
import traceback
import time

router = APIRouter()

@router.post("/retail/adjust/lpsolve", operation_id="retail_adjust_lpsolve")
async def retail_adjust_lpsolve(cust_adjust_position: CustAdjustPosition):
    start_time = time.time()

    with logger.contextualize(request_id=cust_adjust_position.request_id):
        logger.info(f"请求参数: {cust_adjust_position.data_dict}, lp问题数: {cust_adjust_position.portfolio_count}")

        ret = dict()

        try:
            retail_fund_adjust_service = RetailFundAdjustService(cust_adjust_position)
            optimal_solution = await retail_fund_adjust_service.multi_process_lp_solve()

            ret["code"] = 0
            ret["message"] = "success"
            ret["data"] = optimal_solution

        except Exception as e:
            logger.error(traceback.format_exc())
            ret["code"] = 9999
            ret["message"] = str(e)


        cost_time = time.time() - start_time   
        ret["cost_time"] = cost_time
        logger.info(f"总耗时{cost_time}秒")

    return ret



@router.post("/retail/adjust/global-optima", operation_id="retail_adjust_globaloptima")
async def get_adjust_global_optima(adjust_solutions: CustLpOptimalSolutionList):
    start_time = time.time()
    solution_data_dict = adjust_solutions.data_dict

    with logger.contextualize(request_id=solution_data_dict["request_id"]):
        logger.info(f"请求参数: {solution_data_dict}")

        ret = dict()

        try:
            global_optimal_solution = getGlobalOptimalSolution(solution_data_dict["adjust_solutions"])

            ret["code"] = 0
            ret["message"] = "success"
            ret["data"] = global_optimal_solution

        except Exception as e:
            logger.error(traceback.format_exc())
            ret["code"] = 9999
            ret["message"] = str(e)


        cost_time = time.time() - start_time   
        ret["cost_time"] = cost_time
        logger.info(f"总耗时{cost_time}秒")

    return ret