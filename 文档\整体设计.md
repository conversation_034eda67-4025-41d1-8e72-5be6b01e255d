
## 1. 项目需求文档
http://dms.intelnal.howbuy.com/pages/viewpage.action?pageId=98857393


## 2. 依赖数据、接口
- 2.1 用户增持产品
    - 接口文档地址：http://fdc.howbuy.qa/apidoc/howbuy-member-remote/8.5.6-zcpztzbg-2/
    - 开发人员：许刚
    - 接口类型：DUBBO
    - 接口数据示例：fdc-zcpztzbg.json
- 2.2 查询基金产品的购买金额的上限、下限
    - 接口文档地址：【查询基金限额信息接口.md】
    - 接口对接人员：师文科
    - 接口类型：DUBBO
    - 接口数据示例：
- 2.3 调仓算法python接口-线性规划求解
    - 测试环境接口地址：http://192.168.211.119:8000/api/v1/retail/adjust/lpsolve
    - 接口文档地址：【零售调仓python接口.md】
    - 接口开发人员：王君毅
    - 原始算法提供：张婷婷
    - 接口类型：HTTP
    - 接口请求及返回示例：Retail_Adjust_Lpsolve_Example.md

- 2.4 调仓算法python接口-获取全局最优解
    - 测试环境接口地址：http://192.168.211.119:8000/api/v1/retail/adjust/global-optima
    - 接口文档地址：【零售调仓python接口.md】
    - 接口开发人员：王君毅
    - 原始算法提供：张婷婷
    - 接口类型：HTTP
    - 接口请求及返回示例：Retail_Adjust_GlobalOptima_Example.md

## 3. 具体计算步骤
(1) 获取2.1接口中的请求参数，得到推荐产品池列表、超配减持后的客户总资产（含现金）及各个资产类别的占比、推荐客户持仓占比。
    **与DS算法的映射关系,请参考【fdc接口说明.xlsx】**

(2) 根据步骤1中获取的推荐产品池列表，调用2.2中接口依次获取客户购买对应基金的金额上限、金额下限。
    - 查询公募基金： 取 minAppAmt 首次最低申请金额、maxAppAmt 最高申请金额
    - 查询投顾产品： 
        - 取portfolioProductLimitCfgs组合限额列表下的 minAppAmt 最低首次申请金额、maxAppAmt 最高申请金额 
        - portfolioProductLimitCfgs中 (组合产品代码 + 中台业务代码 + 投资者类型) 唯一

(3) 全球配置宝、国内大类配置最多各取1个型号，超过的需要剔除，以2.1接口数据为例说明：
   - recommendProductPool.code = '多策略' && recommendProductPool.productPriority = '1' 为全球配置宝产品， 如果生成的投资组合中包含2个以上该产品，则该组合剔除。

   - recommendProductPool.code = '多策略' && recommendProductPool.productPriority = '2' 为国内大类配置产品，如果生成的投资组合中包含2个以上该产品，则该组合剔除。

   - **后续步骤，对于超过2个产品的调仓方案，需要按照该规则进行筛选剔除。**

(4) 根据推荐产品池列表，生成调仓方案的投资组合列表。开始时，每个投资组合中仅包含1个产品。

(5) 将投资组合列表、推荐产品池列表（包含购买基金的金额上下限）、客户总资产（含现金）及各个资产类别的占比、推荐客户持仓占比作为输入，调用2.3中接口，获取调仓方案。

(6) 如果步骤5有结果返回，则停止计算，返回数据。如果没有结果返回，则继续往下执行。**如果调用2.3接口时采用分批并行计算，则需要对2.3接口返回的数据进行归集，然后调用2.4接口，获取最终的全局最优方案。**

(7) 继续生成投资组合列表，每个投资组合中包含2个产品，执行步骤5和6中的相同操作，如果有结果返回，则停止计算，返回数据。如果没有结果返回，则继续生成投资组合列表，每个投资组合中包含3个产品， 以此类推，直到每个投资组合中包含5个产品，即：每个投资组合中最多有5个产品。

