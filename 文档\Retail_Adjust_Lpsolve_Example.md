
## 1. 接口请求示例


curl -X 'POST' \
  'http://192.168.211.119:8000/api/v1/retail/adjust/lpsolve' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
	"request_id": "cd03386e-e02a-4998-8885-f81c38ed593f",
	"cust_position_holding": {
		"hbone_no": "80000001",
		"holding_market_cap": 1000000.0,
		"holding_cash_amount": 483100.0,
		"holding_cash_proportion": 0.4831,
		"holding_bonds_domestic_proportion": 0.1,
		"holding_bonds_overseas_proportion": 0.1,
		"holding_stocks_domestic_proportion": 0.0728,
		"holding_stocks_overseas_proportion": 0.2178,
		"holding_commodity_proportion": 0.0263,
		"recommend_bonds_domestic_proportion": 0.1,
		"recommend_bonds_overseas_proportion": 0.1,
		"recommend_stocks_domestic_proportion": 0.35,
		"recommend_stocks_overseas_proportion": 0.35,
		"recommend_commodity_proportion": 0.1
	},
	"recommend_product_pool": [{
		"product_code": "P00001",
		"product_type": "zh",
		"stocks_domestic_proportion": 0.0,
		"stocks_overseas_proportion": 0.0,
		"bonds_domestic_proportion": 1.0,
		"bonds_overseas_proportion": 0.0,
		"commodity_proportion": 0.0,
		"product_priority": "1",
		"strategy_type": "singleStra",
		"lower_amount_limit": 10.0,
		"upper_amount_limit": 999999999.0
	}, {
		"product_code": "P00002",
		"product_type": "zh",
		"stocks_domestic_proportion": 0.0,
		"stocks_overseas_proportion": 0.0,
		"bonds_domestic_proportion": 0.0,
		"bonds_overseas_proportion": 1.0,
		"commodity_proportion": 0.0,
		"product_priority": "1",
		"strategy_type": "singleStra",
		"lower_amount_limit": 10.0,
		"upper_amount_limit": 999999999.0
	}, {
		"product_code": "P00003",
		"product_type": "zh",
		"stocks_domestic_proportion": 1.0,
		"stocks_overseas_proportion": 0.0,
		"bonds_domestic_proportion": 0.0,
		"bonds_overseas_proportion": 0.0,
		"commodity_proportion": 0.0,
		"product_priority": "1",
		"strategy_type": "singleStra",
		"lower_amount_limit": 10.0,
		"upper_amount_limit": 999999999.0
	}, {
		"product_code": "P00004",
		"product_type": "zh",
		"stocks_domestic_proportion": 1.0,
		"stocks_overseas_proportion": 0.0,
		"bonds_domestic_proportion": 0.0,
		"bonds_overseas_proportion": 0.0,
		"commodity_proportion": 0.0,
		"product_priority": "1",
		"strategy_type": "singleStra",
		"lower_amount_limit": 10.0,
		"upper_amount_limit": 999999999.0
	}, {
		"product_code": "P00005",
		"product_type": "zh",
		"stocks_domestic_proportion": 0.0,
		"stocks_overseas_proportion": 1.0,
		"bonds_domestic_proportion": 0.0,
		"bonds_overseas_proportion": 0.0,
		"commodity_proportion": 0.0,
		"product_priority": "1",
		"strategy_type": "singleStra",
		"lower_amount_limit": 10.0,
		"upper_amount_limit": 999999999.0
	}, {
		"product_code": "P00006",
		"product_type": "fund",
		"stocks_domestic_proportion": 0.0,
		"stocks_overseas_proportion": 0.0,
		"bonds_domestic_proportion": 0.0,
		"bonds_overseas_proportion": 0.0,
		"commodity_proportion": 1.0,
		"product_priority": "1",
		"strategy_type": "singleStra",
		"lower_amount_limit": 10.0,
		"upper_amount_limit": 999999999.0
	}, {
		"product_code": "P00007",
		"product_type": "zh",
		"stocks_domestic_proportion": 0.0,
		"stocks_overseas_proportion": 0.0,
		"bonds_domestic_proportion": 0.58,
		"bonds_overseas_proportion": 0.42,
		"commodity_proportion": 0.0,
		"product_priority": "1",
		"strategy_type": "doubleStra",
		"lower_amount_limit": 10.0,
		"upper_amount_limit": 999999999.0
	}, {
		"product_code": "P00008",
		"product_type": "zh",
		"stocks_domestic_proportion": 0.08,
		"stocks_overseas_proportion": 0.02,
		"bonds_domestic_proportion": 0.75,
		"bonds_overseas_proportion": 0.1,
		"commodity_proportion": 0.05,
		"product_priority": "1",
		"strategy_type": "multiStra",
		"lower_amount_limit": 10.0,
		"upper_amount_limit": 1000000.0
	}, {
		"product_code": "P00009",
		"product_type": "zh",
		"stocks_domestic_proportion": 0.4,
		"stocks_overseas_proportion": 0.0,
		"bonds_domestic_proportion": 0.5,
		"bonds_overseas_proportion": 0.0,
		"commodity_proportion": 0.1,
		"product_priority": "2",
		"strategy_type": "multiStra",
		"lower_amount_limit": 10.0,
		"upper_amount_limit": 999999999.0
	}, {
		"product_code": "P00010",
		"product_type": "zh",
		"stocks_domestic_proportion": 0.35,
		"stocks_overseas_proportion": 0.15,
		"bonds_domestic_proportion": 0.4,
		"bonds_overseas_proportion": 0.05,
		"commodity_proportion": 0.05,
		"product_priority": "1",
		"strategy_type": "multiStra",
		"lower_amount_limit": 10.0,
		"upper_amount_limit": 1000000.0
	}, {
		"product_code": "P00011",
		"product_type": "zh",
		"stocks_domestic_proportion": 0.7,
		"stocks_overseas_proportion": 0.0,
		"bonds_domestic_proportion": 0.2,
		"bonds_overseas_proportion": 0.0,
		"commodity_proportion": 0.1,
		"product_priority": "2",
		"strategy_type": "multiStra",
		"lower_amount_limit": 10.0,
		"upper_amount_limit": 999999999.0
	}, {
		"product_code": "P00012",
		"product_type": "zh",
		"stocks_domestic_proportion": 0.5,
		"stocks_overseas_proportion": 0.25,
		"bonds_domestic_proportion": 0.2,
		"bonds_overseas_proportion": 0.0,
		"commodity_proportion": 0.05,
		"product_priority": "1",
		"strategy_type": "multiStra",
		"lower_amount_limit": 10.0,
		"upper_amount_limit": 1000000.0
	}],
	"portfolio_list": [
		["P00001", "P00002", "P00003"],
		["P00001", "P00002", "P00004"],
		["P00001", "P00002", "P00005"],
		["P00001", "P00002", "P00006"],
		["P00001", "P00002", "P00007"],
		["P00001", "P00002", "P00008"],
		["P00001", "P00002", "P00009"],
		["P00001", "P00002", "P00010"],
		["P00001", "P00002", "P00011"],
		["P00001", "P00002", "P00012"],
		["P00001", "P00003", "P00004"],
		["P00001", "P00003", "P00005"],
		["P00001", "P00003", "P00006"],
		["P00001", "P00003", "P00007"],
		["P00001", "P00003", "P00008"],
		["P00001", "P00003", "P00009"],
		["P00001", "P00003", "P00010"],
		["P00001", "P00003", "P00011"],
		["P00001", "P00003", "P00012"],
		["P00001", "P00004", "P00005"],
		["P00001", "P00004", "P00006"],
		["P00001", "P00004", "P00007"],
		["P00001", "P00004", "P00008"],
		["P00001", "P00004", "P00009"],
		["P00001", "P00004", "P00010"],
		["P00001", "P00004", "P00011"],
		["P00001", "P00004", "P00012"],
		["P00001", "P00005", "P00006"],
		["P00001", "P00005", "P00007"],
		["P00001", "P00005", "P00008"],
		["P00001", "P00005", "P00009"],
		["P00001", "P00005", "P00010"],
		["P00001", "P00005", "P00011"],
		["P00001", "P00005", "P00012"],
		["P00001", "P00006", "P00007"],
		["P00001", "P00006", "P00008"],
		["P00001", "P00006", "P00009"],
		["P00001", "P00006", "P00010"],
		["P00001", "P00006", "P00011"],
		["P00001", "P00006", "P00012"],
		["P00001", "P00007", "P00008"],
		["P00001", "P00007", "P00009"],
		["P00001", "P00007", "P00010"],
		["P00001", "P00007", "P00011"],
		["P00001", "P00007", "P00012"],
		["P00001", "P00008", "P00009"],
		["P00001", "P00008", "P00011"],
		["P00001", "P00009", "P00010"],
		["P00001", "P00009", "P00012"],
		["P00001", "P00010", "P00011"],
		["P00001", "P00011", "P00012"],
		["P00002", "P00003", "P00004"],
		["P00002", "P00003", "P00005"],
		["P00002", "P00003", "P00006"],
		["P00002", "P00003", "P00007"],
		["P00002", "P00003", "P00008"],
		["P00002", "P00003", "P00009"],
		["P00002", "P00003", "P00010"],
		["P00002", "P00003", "P00011"],
		["P00002", "P00003", "P00012"],
		["P00002", "P00004", "P00005"],
		["P00002", "P00004", "P00006"],
		["P00002", "P00004", "P00007"],
		["P00002", "P00004", "P00008"],
		["P00002", "P00004", "P00009"],
		["P00002", "P00004", "P00010"],
		["P00002", "P00004", "P00011"],
		["P00002", "P00004", "P00012"],
		["P00002", "P00005", "P00006"],
		["P00002", "P00005", "P00007"],
		["P00002", "P00005", "P00008"],
		["P00002", "P00005", "P00009"],
		["P00002", "P00005", "P00010"],
		["P00002", "P00005", "P00011"],
		["P00002", "P00005", "P00012"],
		["P00002", "P00006", "P00007"],
		["P00002", "P00006", "P00008"],
		["P00002", "P00006", "P00009"],
		["P00002", "P00006", "P00010"],
		["P00002", "P00006", "P00011"],
		["P00002", "P00006", "P00012"],
		["P00002", "P00007", "P00008"],
		["P00002", "P00007", "P00009"],
		["P00002", "P00007", "P00010"],
		["P00002", "P00007", "P00011"],
		["P00002", "P00007", "P00012"],
		["P00002", "P00008", "P00009"],
		["P00002", "P00008", "P00011"],
		["P00002", "P00009", "P00010"],
		["P00002", "P00009", "P00012"],
		["P00002", "P00010", "P00011"],
		["P00002", "P00011", "P00012"],
		["P00003", "P00004", "P00005"],
		["P00003", "P00004", "P00006"],
		["P00003", "P00004", "P00007"],
		["P00003", "P00004", "P00008"],
		["P00003", "P00004", "P00009"],
		["P00003", "P00004", "P00010"],
		["P00003", "P00004", "P00011"],
		["P00003", "P00004", "P00012"],
		["P00003", "P00005", "P00006"],
		["P00003", "P00005", "P00007"],
		["P00003", "P00005", "P00008"],
		["P00003", "P00005", "P00009"],
		["P00003", "P00005", "P00010"],
		["P00003", "P00005", "P00011"],
		["P00003", "P00005", "P00012"],
		["P00003", "P00006", "P00007"],
		["P00003", "P00006", "P00008"],
		["P00003", "P00006", "P00009"],
		["P00003", "P00006", "P00010"],
		["P00003", "P00006", "P00011"],
		["P00003", "P00006", "P00012"],
		["P00003", "P00007", "P00008"],
		["P00003", "P00007", "P00009"],
		["P00003", "P00007", "P00010"],
		["P00003", "P00007", "P00011"],
		["P00003", "P00007", "P00012"],
		["P00003", "P00008", "P00009"],
		["P00003", "P00008", "P00011"],
		["P00003", "P00009", "P00010"],
		["P00003", "P00009", "P00012"],
		["P00003", "P00010", "P00011"],
		["P00003", "P00011", "P00012"],
		["P00004", "P00005", "P00006"],
		["P00004", "P00005", "P00007"],
		["P00004", "P00005", "P00008"],
		["P00004", "P00005", "P00009"],
		["P00004", "P00005", "P00010"],
		["P00004", "P00005", "P00011"],
		["P00004", "P00005", "P00012"],
		["P00004", "P00006", "P00007"],
		["P00004", "P00006", "P00008"],
		["P00004", "P00006", "P00009"],
		["P00004", "P00006", "P00010"],
		["P00004", "P00006", "P00011"],
		["P00004", "P00006", "P00012"],
		["P00004", "P00007", "P00008"],
		["P00004", "P00007", "P00009"],
		["P00004", "P00007", "P00010"],
		["P00004", "P00007", "P00011"],
		["P00004", "P00007", "P00012"],
		["P00004", "P00008", "P00009"],
		["P00004", "P00008", "P00011"],
		["P00004", "P00009", "P00010"],
		["P00004", "P00009", "P00012"],
		["P00004", "P00010", "P00011"],
		["P00004", "P00011", "P00012"],
		["P00005", "P00006", "P00007"],
		["P00005", "P00006", "P00008"],
		["P00005", "P00006", "P00009"],
		["P00005", "P00006", "P00010"],
		["P00005", "P00006", "P00011"],
		["P00005", "P00006", "P00012"],
		["P00005", "P00007", "P00008"],
		["P00005", "P00007", "P00009"],
		["P00005", "P00007", "P00010"],
		["P00005", "P00007", "P00011"],
		["P00005", "P00007", "P00012"],
		["P00005", "P00008", "P00009"],
		["P00005", "P00008", "P00011"],
		["P00005", "P00009", "P00010"],
		["P00005", "P00009", "P00012"],
		["P00005", "P00010", "P00011"],
		["P00005", "P00011", "P00012"],
		["P00006", "P00007", "P00008"],
		["P00006", "P00007", "P00009"],
		["P00006", "P00007", "P00010"],
		["P00006", "P00007", "P00011"],
		["P00006", "P00007", "P00012"],
		["P00006", "P00008", "P00009"],
		["P00006", "P00008", "P00011"],
		["P00006", "P00009", "P00010"],
		["P00006", "P00009", "P00012"],
		["P00006", "P00010", "P00011"],
		["P00006", "P00011", "P00012"],
		["P00007", "P00008", "P00009"],
		["P00007", "P00008", "P00011"],
		["P00007", "P00009", "P00010"],
		["P00007", "P00009", "P00012"],
		["P00007", "P00010", "P00011"],
		["P00007", "P00011", "P00012"]
	]
}'


## 2. 返回结果示例

{
  "code": 0,
  "message": "success",
  "data": {
    "hbone_no": "80000001",
    "fund_num": 3,
    "strategy_priority_min": 1,
    "strategy_priority_sum": 7,
    "product_priority_min": 1,
    "product_priority_sum": 4,
    "deviation_avg": 0.029479879999999997,
    "adjust_solution": [
      {
        "product_code": "P00003",
        "product_type": "zh",
        "adjust_amount": 56100
      },
      {
        "product_code": "P00005",
        "product_type": "zh",
        "adjust_amount": 181333
      },
      {
        "product_code": "P00011",
        "product_type": "zh",
        "adjust_amount": 245667
      }
    ]
  },
  "cost_time": 0.00518488883972168
}
