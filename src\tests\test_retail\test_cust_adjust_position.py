"""
客户调仓类的测试用例
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import pytest
from pydantic import ValidationError
from models.retail import CustAdjustPosition, CustPositionHolding, RecommendProduct, StrategyType


class TestCustAdjustPosition:
    """客户调仓测试类"""
    
    def create_sample_position_holding(self):
        """创建示例客户持仓信息"""
        return CustPositionHolding(
            hbone_no="123456789",
            holding_bonds_domestic_proportion=0.30,
            holding_bonds_overseas_proportion=0.10,
            holding_cash_amount=50000.0,
            holding_cash_proportion=0.05,
            holding_commodity_proportion=0.15,
            holding_market_cap=1000000.0,
            holding_stocks_domestic_proportion=0.25,
            holding_stocks_overseas_proportion=0.15,
            recommend_bonds_domestic_proportion=0.35,
            recommend_bonds_overseas_proportion=0.15,
            recommend_commodity_proportion=0.10,
            recommend_stocks_domestic_proportion=0.25,
            recommend_stocks_overseas_proportion=0.15
        )
    
    def create_sample_product_pool(self):
        """创建示例推荐产品池"""
        return [
            RecommendProduct(
                product_code="P00001",
                product_type="zh",
                stocks_domestic_proportion=0.25,
                stocks_overseas_proportion=0.15,
                bonds_domestic_proportion=0.30,
                bonds_overseas_proportion=0.10,
                commodity_proportion=0.20,
                product_priority='1',
                strategy_type=StrategyType.MULTI_STRATEGY,
                lower_amount_limit=1000.0,
                upper_amount_limit=10000000.0
            ),
            RecommendProduct(
                product_code="P00002",
                product_type="fund",
                stocks_domestic_proportion=0.40,
                stocks_overseas_proportion=0.20,
                bonds_domestic_proportion=0.25,
                bonds_overseas_proportion=0.05,
                commodity_proportion=0.10,
                product_priority='1',
                strategy_type=StrategyType.SINGLE_STRATEGY,
                lower_amount_limit=5000.0,
                upper_amount_limit=5000000.0
            ),
            RecommendProduct(
                product_code="P00002",
                product_type="zh",
                stocks_domestic_proportion=0.30,
                stocks_overseas_proportion=0.10,
                bonds_domestic_proportion=0.35,
                bonds_overseas_proportion=0.15,
                commodity_proportion=0.10,
                product_priority='1',
                strategy_type=StrategyType.DUAL_STRATEGY,
                lower_amount_limit=10000.0,
                upper_amount_limit=1000000.0
            )
        ]
    
    def test_valid_adjust_position_creation(self):
        """测试创建有效的客户调仓"""
        position_holding = self.create_sample_position_holding()
        product_pool = self.create_sample_product_pool()
        
        adjust_position = CustAdjustPosition(
            request_id="REQ_20241226_001",
            cust_position_holding=position_holding,
            recommend_product_pool=product_pool,
            portfolio_list=[
                ["H00001"],
                ["P00001", "H00001"],
                ["P00001", "H00001", "P00002"]
            ]
        )
        
        assert adjust_position.request_id == "REQ_20241226_001"
        assert adjust_position.cust_position_holding.hbone_no == "123456789"
        assert adjust_position.product_count == 3
        assert len(adjust_position.product_codes) == 3
    
    def test_request_id_validation_empty(self):
        """测试请求ID为空时的验证错误"""
        position_holding = self.create_sample_position_holding()
        product_pool = self.create_sample_product_pool()
        
        with pytest.raises(ValidationError) as exc_info:
            CustAdjustPosition(
                request_id="",  # 空请求ID
                cust_position_holding=position_holding,
                recommend_product_pool=product_pool
            )
        
        assert "请求ID不能为空" in str(exc_info.value)
    
    def test_request_id_validation_whitespace(self):
        """测试请求ID只有空格时的验证错误"""
        position_holding = self.create_sample_position_holding()
        product_pool = self.create_sample_product_pool()
        
        with pytest.raises(ValidationError) as exc_info:
            CustAdjustPosition(
                request_id="   ",  # 只有空格
                cust_position_holding=position_holding,
                recommend_product_pool=product_pool
            )
        
        assert "请求ID不能为空" in str(exc_info.value)
    
    def test_empty_product_pool_validation(self):
        """测试空推荐产品池的验证错误"""
        position_holding = self.create_sample_position_holding()
        
        with pytest.raises(ValidationError) as exc_info:
            CustAdjustPosition(
                request_id="REQ_20241226_001",
                cust_position_holding=position_holding,
                recommend_product_pool=[]  # 空产品池
            )
        
        assert "推荐产品池不能为空" in str(exc_info.value)
    
    def test_duplicate_product_code_validation(self):
        """测试重复基金代码的验证错误"""
        position_holding = self.create_sample_position_holding()
        
        # 创建包含重复基金代码的产品池
        duplicate_product_pool = [
            RecommendProduct(
                product_code="P00001",
                product_type="zh",
                stocks_domestic_proportion=0.25,
                stocks_overseas_proportion=0.15,
                bonds_domestic_proportion=0.30,
                bonds_overseas_proportion=0.10,
                commodity_proportion=0.20,
                product_priority='1',
                strategy_type=StrategyType.MULTI_STRATEGY,
                lower_amount_limit=1000.0,
                upper_amount_limit=10000000.0
            ),
            RecommendProduct(
                product_code="P00001",  # 重复的基金代码
                product_type="fund",
                stocks_domestic_proportion=0.30,
                stocks_overseas_proportion=0.20,
                bonds_domestic_proportion=0.25,
                bonds_overseas_proportion=0.15,
                commodity_proportion=0.10,
                product_priority='1',
                strategy_type=StrategyType.SINGLE_STRATEGY,
                lower_amount_limit=5000.0,
                upper_amount_limit=5000000.0
            )
        ]
        
        with pytest.raises(ValidationError) as exc_info:
            CustAdjustPosition(
                request_id="REQ_20241226_001",
                cust_position_holding=position_holding,
                recommend_product_pool=duplicate_product_pool
            )
        
        assert "推荐产品池中不能包含重复的基金代码" in str(exc_info.value)

    def test_empty_portfolio_list_validation(self):
        """测试空产品组合列表的验证错误"""
        position_holding = self.create_sample_position_holding()
        product_pool = self.create_sample_product_pool()

        with pytest.raises(ValidationError) as exc_info:
            CustAdjustPosition(
                request_id="REQ_20241226_001",
                cust_position_holding=position_holding,
                recommend_product_pool=product_pool,
                portfolio_list=[]  # 空产品组合列表
            )

        assert "投资产品组合列表不能为空" in str(exc_info.value)

    def test_empty_portfolio_validation(self):
        """测试空产品组合的验证错误"""
        position_holding = self.create_sample_position_holding()
        product_pool = self.create_sample_product_pool()

        with pytest.raises(ValidationError) as exc_info:
            CustAdjustPosition(
                request_id="REQ_20241226_001",
                cust_position_holding=position_holding,
                recommend_product_pool=product_pool,
                portfolio_list=[
                    ["H04066"],
                    [],  # 空的产品组合
                    ["SVX355"]
                ]
            )

        assert "第2个产品组合不能为空" in str(exc_info.value)

    def test_duplicate_product_code_in_portfolio_validation(self):
        """测试产品组合内重复基金代码的验证错误"""
        position_holding = self.create_sample_position_holding()
        product_pool = self.create_sample_product_pool()

        with pytest.raises(ValidationError) as exc_info:
            CustAdjustPosition(
                request_id="REQ_20241226_001",
                cust_position_holding=position_holding,
                recommend_product_pool=product_pool,
                portfolio_list=[
                    ["P00001"],
                    ["H04066", "H04066"],  # 组合内重复基金代码
                ]
            )

        assert "第2个产品组合中不能包含重复的基金代码" in str(exc_info.value)

    def test_invalid_product_code_in_portfolio_validation(self):
        """测试产品组合中包含不在推荐产品池中的基金代码"""
        position_holding = self.create_sample_position_holding()
        product_pool = self.create_sample_product_pool()

        with pytest.raises(ValidationError) as exc_info:
            CustAdjustPosition(
                request_id="REQ_20241226_001",
                cust_position_holding=position_holding,
                recommend_product_pool=product_pool,
                portfolio_list=[
                    ["P00001"],
                    ["INVALID_CODE"],  # 不在推荐产品池中的基金代码
                    ["H00001"]
                ]
            )

        assert "不在推荐产品池中" in str(exc_info.value)


if __name__ == "__main__":
    # 运行简单的测试示例
    try:
        # 创建示例数据
        position_holding = CustPositionHolding(
            hbone_no="123456789",
            holding_bonds_domestic_proportion=0.30,
            holding_bonds_overseas_proportion=0.10,
            holding_cash_amount=50000.0,
            holding_cash_proportion=0.05,
            holding_commodity_proportion=0.15,
            holding_market_cap=1000000.0,
            holding_stocks_domestic_proportion=0.25,
            holding_stocks_overseas_proportion=0.15,
            recommend_bonds_domestic_proportion=0.35,
            recommend_bonds_overseas_proportion=0.15,
            recommend_commodity_proportion=0.10,
            recommend_stocks_domestic_proportion=0.25,
            recommend_stocks_overseas_proportion=0.15
        )
        
        product_pool = [
            RecommendProduct(
                product_code="H04066",
                product_type="fund",
                stocks_domestic_proportion=0.25,
                stocks_overseas_proportion=0.15,
                bonds_domestic_proportion=0.30,
                bonds_overseas_proportion=0.10,
                commodity_proportion=0.20,
                product_priority='1',
                strategy_type=StrategyType.MULTI_STRATEGY,
                lower_amount_limit=1000.0,
                upper_amount_limit=10000000.0
            ),
            RecommendProduct(
                product_code="SVX355",
                product_type="zh",
                stocks_domestic_proportion=0.40,
                stocks_overseas_proportion=0.20,
                bonds_domestic_proportion=0.25,
                bonds_overseas_proportion=0.05,
                commodity_proportion=0.10,
                product_priority='1',
                strategy_type=StrategyType.SINGLE_STRATEGY,
                lower_amount_limit=5000.0,
                upper_amount_limit=5000000.0
            )
        ]
        
        # 创建客户调仓实例
        adjust_position = CustAdjustPosition(
            request_id="  REQ_20241226_001  ",  # 测试空格处理
            cust_position_holding=position_holding,
            recommend_product_pool=product_pool,
            portfolio_list=[
                ["H04066"],
                ["SVX355"],
                ["H04066", "SVX355"]
            ]
        )

        print("✅ 客户调仓创建成功!")
        print(f"请求ID: {adjust_position.request_id}")
        print(f"客户一账通号: {adjust_position.cust_position_holding.hbone_no}")
        print(f"推荐产品数量: {adjust_position.product_count}")
        print(f"产品组合数量: {adjust_position.portfolio_count}")


        # 获取数据字典
        data_dict = adjust_position.to_data_dict
        print(f"数据字典: {data_dict}")

    except ValidationError as e:
        print(f"❌ 验证错误: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")
