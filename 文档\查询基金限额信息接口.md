
## 1. 查询公募基金

dubbo:com.howbuy.interlayer.product.service.fund.QueryFundInfoService.getFundLimitInfo

>/**
     * @api {get}
     *      dubbo:com.howbuy.interlayer.product.service.fund.QueryFundInfoService.getFundLimitInfo(fundCode,shareClass, busiCode, invstType) 查询基金限额信息
     * @apiGroup fund
     * @apiDescription 查询基金限额信息
     * 
     * @apiParam {String} fundCode 基金代码，不能为空
     * @apiParam {String} shareClass 份额类型
     * @apiParam {String} busiCode 业务码，020-认购；022-赎回；024-赎回；036-转换
     * @apiParam {String} invstType 客户类型，0-机构；1-个人
     * @apiParamExample {json} Request Example 
     * dubbo com.howbuy.interlayer.product.service.fund.QueryFundInfoService.getFundLimitInfo
     * 
     * @apiSuccess {fundLimitModel} fundLimitModel
     * 
     * @apiSuccess (fundLimitModel) {String} fundCode 基金代码
     * @apiSuccess (fundLimitModel) {String} shareClass 份额类型
     * @apiSuccess (fundLimitModel) {String} busiCode 业务类型
     * @apiSuccess (fundLimitModel) {String} invstType 投资者类型
     * @apiSuccess (fundLimitModel) {String} custType 户类别
     * @apiSuccess (fundLimitModel) {BigDecimal} minAppAmt 首次最低申请金额
     * @apiSuccess (fundLimitModel) {BigDecimal} minAppVol 首次最低申请份额
     * @apiSuccess (fundLimitModel) {BigDecimal} minSuppleAmt 最低追加申请金额
     * @apiSuccess (fundLimitModel) {BigDecimal} minSuppleVol 最低追加申请份额
     * @apiSuccess (fundLimitModel) {BigDecimal} maxAppAmt 最高申请金额
     * @apiSuccess (fundLimitModel) {BigDecimal} maxAppVol 最高申请份额
     * @apiSuccess (fundLimitModel) {BigDecimal} maxSumAmt 每日最大累计申请金额
     * @apiSuccess (fundLimitModel) {BigDecimal} maxSumVol 每日最大累计申请份额
     * @apiSuccess (fundLimitModel) {BigDecimal} netMinAppAmt 首次最低申请金额-净购买金额
     * @apiSuccess (fundLimitModel) {BigDecimal} netMinSuppleAmt 最低追加申请金额-净追加金额
     * 
     * @apiSuccessExample {json} Response Example
     *                  dubbo com.howbuy.interlayer.product.service.fund.QueryFundInfoService.getFundLimitInfo
     * 
*/


## 2. 查询投顾产品
dubbo:com.howbuy.interlayer.product.service.AdviserProductService.getAdviserProductByDisCode

/**
     * @api {get} dubbo:com.howbuy.interlayer.product.service.AdviserProductService.getAdviserProductByDisCode 根据分销机构和产品代码查询投顾产品信息接口
     * @apiGroup adviser
     * @apiDescription 根据分销机构和产品代码查询投顾产品信息接口
     *
     * @apiParam {String} disCode 分销机构号
     * @apiParam {String} productCode 组合产品代码
     * @apiParamExample {json} Request Example
     * dubbo java.lang.String
     *
     * @apiSuccess {String} productCode 组合产品代码
     * @apiSuccess {String} productName 组合产品名称
     * @apiSuccess {String} subTypeCode 子类型编码
     * @apiSuccess {Integer} changeVersion 变更版本
     * @apiSuccess {String} productStatus 组合产品状态 : 0-无效, 1-正常
     * @apiSuccess {String} ristLevel 组合产品风险级别 : 1-低风险,2-中风险,3-高风险
     * @apiSuccess {BigDecimal} deviationRatio 偏离度
     * @apiSuccess {BigDecimal} fluctuationRatio 波动率
     * @apiSuccess {BigDecimal} averageYield 收益平均率
     * @apiSuccess {String} strategySummary 策略概要
     * @apiSuccess {String} operationMode 操作方式
     * @apiSuccess {String} rateDesc 费率说明
     * @apiSuccess {String} strategyUrl 策略地址
     * @apiSuccess {String} displayFlag 展示标记
     * @apiSuccess {String} isBuyOpen 购买是否开通 : 0-未开通,1-已开通
     * @apiSuccess {String} isSoldOpen 卖出是否开通 : 0-未开通,1-已开通
     * @apiSuccess {String} isBarBalanceOpen 拉杆平衡是否开通 : 0-未开通,1-已开通
     * @apiSuccess {String} isWaveBalanceOpen 波动平衡是否开通 : 0-未开通,1-已开通
     * @apiSuccess {BigDecimal} minAppAmt 最低首次申请金额
     * @apiSuccess {BigDecimal} minAppVol 最低首次申请份额
     * @apiSuccess {BigDecimal} minSuppleAmt 最低追加申请金额
     * @apiSuccess {BigDecimal} maxAppAmt 最高申请金额
     * @apiSuccess {BigDecimal} maxAppVol 最高申请份额
     * @apiSuccess {BigDecimal} maxSumAmt 累计最大金额
     * @apiSuccess {BigDecimal} maxSumVol 累计最大份额
     * @apiSuccess {BigDecimal} ratio 类型别名为股票的占比从低到高进行排序
     * @apiSuccess {String} productType 产品类型，与portfolioTypeCode值相同
     * @apiSuccess {String} portfolioTypeCode 组合产品类型代码
     * @apiSuccess {String} pauseThreshold 暂停阀值
     * @apiSuccess {String} payFundCode 支付基金代码
     * @apiSuccess {String} payFundType 支付基金类型 3-货币  别的非货币
     * @apiSuccess {String} partnerCode 商户号
     * @apiSuccess {String} partnerName 商户名称
     *
     * @apiSuccess {PortfolioProductTxOpenCfgModel} portfolioProductTxOpenCfgModel 组合交易开通
     * @apiSuccess (portfolioProductTxOpenCfgModel) {List} portfolioProductTxOpenCfgs 组合交易开通列表
     * @apiSuccess (portfolioProductTxOpenCfgs) {String} recordNo 记录号
     * @apiSuccess (portfolioProductTxOpenCfgs) {String} productCode 组合产品代码
     * @apiSuccess (portfolioProductTxOpenCfgs) {String} txCode 交易代码
     * @apiSuccess (portfolioProductTxOpenCfgs) {String} openStatus 功能开通状态：0-未开通 1-已开通
     * @apiSuccess (portfolioProductTxOpenCfgs) {String} taCode TA代码
     *
     * @apiSuccess {PortfolioProductLimitCfgModel} portfolioProductLimitCfgModel 组合限额
     * @apiSuccess (portfolioProductLimitCfgModel) {List} portfolioProductLimitCfgs 组合限额列表
     * @apiSuccess (portfolioProductLimitCfgs) {String} productCode 组合产品代码
     * @apiSuccess (portfolioProductLimitCfgs) {String} fundShareClass 基金份额类型
     * @apiSuccess (portfolioProductLimitCfgs) {String} mBusiCode 中台业务代码
     * @apiSuccess (portfolioProductLimitCfgs) {BigDecimal} minAppAmt 最低首次申请金额
     * @apiSuccess (portfolioProductLimitCfgs) {BigDecimal} minAppVol 最低首次申请份额
     * @apiSuccess (portfolioProductLimitCfgs) {BigDecimal} minSuppleAmt 最低追加申请金额
     * @apiSuccess (portfolioProductLimitCfgs) {BigDecimal} minSuppleVol 最低追加申请份额
     * @apiSuccess (portfolioProductLimitCfgs) {BigDecimal} maxAppAmt 最高申请金额
     * @apiSuccess (portfolioProductLimitCfgs) {BigDecimal} maxAppVol 最高申请份额
     * @apiSuccess (portfolioProductLimitCfgs) {BigDecimal} maxSumAmt 累计最大金额
     * @apiSuccess (portfolioProductLimitCfgs) {BigDecimal} maxSumVol 累计最大份额
     * @apiSuccess (portfolioProductLimitCfgs) {String} invstType 投资者类型
     * @apiSuccess (portfolioProductLimitCfgs) {String} tradeModel 交易方式
     * @apiSuccess (portfolioProductLimitCfgs) {String} tradeUnit 交易单位
     *
     * @apiSuccessExample {json} Response Example
     * dubbo com.howbuy.interlayer.product.model.adviser.AdviserPortfolioProductModel
     *
     */

