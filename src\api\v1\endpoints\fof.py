from fastapi import APIRouter
from models.fof import CustOptimizeRatio
from services.fof_optimiz_service import FofOptimizeService
from loguru import logger
import traceback
import time
import uuid

router = APIRouter()

@router.post("/fof/optimiz/{hbone_no}", operation_id="hbc_fof_optimiz")
async def optimiz_fof_portfolio(hbone_no: str, cust_optimize_ratio: CustOptimizeRatio):
    start_time = time.time()
    request_id = str(uuid.uuid1())

    with logger.contextualize(request_id=request_id):
        logger.info(f"请求参数: 客户一账通={hbone_no}，产品池=【{cust_optimize_ratio.fof_portfolio}】，客户持仓策略分布=【{cust_optimize_ratio.cust_hold_ratio}】，"
                    f"客户推荐配置分布=【{cust_optimize_ratio.cust_recommend_ratio}】, 求解问题规模={len(cust_optimize_ratio.lp_problem_list)}")

        ret = dict()

        try:
            fof_optimize_service = FofOptimizeService(request_id, hbone_no, cust_optimize_ratio)
            output_records = await fof_optimize_service.multi_process_fof_lp_solve()

            ret["code"] = 0
            ret["message"] = "success"
            ret["data"] = output_records

        except Exception as e:
            logger.error(traceback.format_exc())
            ret["code"] = 9999
            ret["message"] = str(e)


        cost_time = time.time() - start_time   
        ret["cost_time"] = cost_time
        logger.info(f"总耗时{cost_time}秒")

    return ret




