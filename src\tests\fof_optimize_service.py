import pandas as pd
import numpy as np
import itertools
from pathlib import Path
# import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import time
from datetime import datetime
import requests
import os
import json
import traceback

STATIC_FILES_DIR = Path(__file__).resolve().parent

def generate_product_comnbinations():

    product_file = os.path.join(STATIC_FILES_DIR, "product.xlsx")       
    input_prd = pd.read_excel(product_file)
    input_prd.fillna(0, inplace=True)

    # v2版本把另类和CTA这两个策略，国内/海外策略合并起来
    input_prd['cta'] = input_prd['cta_gn'] + input_prd['cta_hw']
    input_prd['ll'] = input_prd['ll_gn'] +  input_prd['ll_hw']

    # 对于有同样策略占比的产品，进行合并处理，减少产品组合情况
    df_prd = input_prd.groupby(['gp_gn','gp_hw','cta','gs_gn','gs_hw','ll','min_amt','fof_type']).agg({
            'fund_code': lambda x: '/'.join(x)
        }).reset_index()

    # 初始化一个空列表来存储要剔除的组合
    dcl_list = []

    # 要剔除的组合情况，包含这些组合的都要剔除
    gn_dcl_list = list(itertools.combinations(df_prd[df_prd['fof_type']=='国内多策略'].index, 2))
    hw_dcl_list = list(itertools.combinations(df_prd[df_prd['fof_type']=='海外多策略'].index, 2))

    dcl_list = gn_dcl_list + hw_dcl_list

    # 初始化一个空列表来存储组合
    product_combinations_list = []

    # 增加限制条件，国内多策略在组合中只能出现一次，海外多策略在组合中也只能出现一次
    # 先取出所有的产品组合情况，用index先提取出所有的组合情况
    for r in range(1, 4):
        product_combinations = list(itertools.combinations(df_prd.index, r))
        # 将组合添加到列表中
        for comb in product_combinations:
            if any(set(sub).issubset(set(comb)) for sub in dcl_list) == False:
                product_combinations_list.append(comb)

    df_prd['index'] = df_prd.index
    return df_prd, product_combinations_list


def send_post_request(pct, url, data):
    print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")}  当前偏离度{pct}----发送请求：{url}')
    headers = {'Content-Type': 'application/json'}

    # print(f"传参：{payload}")
    print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")}  当前偏离度{pct}----传入组合个数：{len(data["lp_problem_list"])}')
        
    start_time = time.time()

    try:
        res = requests.post(url, data=json.dumps(data), headers=headers)
        # print(f"返回结果: {res.text}")
        res_json = res.json()
        print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")}  当前偏离度{pct}----有解组合数：{len(res_json.get("data"))}')
        print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")}  当前偏离度{pct}----请求耗时: {time.time() - start_time} ----求解耗时: {res_json.get("cost_time")}')
        return res.json()
    except Exception as ex:
        # traceback.print_exc()
        print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")}  当前偏离度{pct}----发送请求失败:{ex}')
        raise ex


if __name__ == "__main__":

    start_time = time.time()
    customer_file = os.path.join(STATIC_FILES_DIR, "customer.xlsx")

    hbone_no = 8000022350
    # url = f"http://127.0.0.1:8080/api/v1/fof/optimiz/{hbone_no}"
    url = f"http://192.168.211.119:8000/api/v1/fof/optimiz/{hbone_no}"

    
   
    # 偏离度范围
    percentages = np.arange(0.05, 1.01, 0.05)

    input_cust = pd.read_excel(customer_file, index_col="hbone_no")
    input_cust.fillna('', inplace=True)

    cust = input_cust.loc[hbone_no, :]
    # print(cust)

    df_prd, product_combinations_list = generate_product_comnbinations()
    
    # 如果客户之前购买过某只产品，则初始值变成10w
    for j in df_prd.index:
        if set(df_prd.loc[j, 'fund_code'].split('/')) & set(cust['fof_fund_cl'].split(',')):
            df_prd.loc[j, 'min_amt'] = 100000
        else:
            df_prd.loc[j, 'min_amt'] = df_prd.loc[j, 'min_amt']

    prd_columns = ["index", "fund_code", "fof_type", "min_amt", "gp_gn", "gp_hw", "cta", "gs_gn", "gs_hw", "ll"]
    cust_cur_columns = ["market_cap", "gp_gn", "gp_hw", "cta", "gs_gn", "gs_hw", "ll"]
    cust_rec_columns = ["gp_gn", "gp_hw", "cta", "gs_gn", "gs_hw", "ll"]

    df_fof_portfolio = df_prd.loc[:, prd_columns].astype({"index": "str", 
                             "fund_code": "str", 
                             "fof_type": "str", 
                             "min_amt": "str", 
                             "gp_gn": "str", 
                             "gp_hw": "str", 
                             "cta": "str", 
                             "gs_gn": "str", 
                             "gs_hw": "str", 
                             "ll": "str"})

    fof_portfolio = {"columns":prd_columns, "data": df_fof_portfolio[prd_columns].values.tolist()}

    cust_hold_ratio = {"columns":cust_cur_columns, 
                       "data": cust.loc[["market_cap", "cur_gp_gn", "cur_gp_hw", "cur_cta", "cur_gs_gn", "cur_gs_hw", "cur_ll"]].values.tolist()
                }
    cust_hold_ratio["data"] = [str(v) for v in cust_hold_ratio["data"]]


    cust_rec_ratio = {"columns":cust_rec_columns, 
                       "data": cust.loc[["rec_gp_gn", "rec_gp_hw", "rec_cta", "rec_gs_gn", "rec_gs_hw", "rec_ll"]].values.tolist()
                    }
    
    cust_rec_ratio["data"] = [str(v) for v in cust_rec_ratio["data"]]
    
    prod_comb_list = [[str(e) for e in prd_comb] for prd_comb in product_combinations_list]

    payload_list = []

    percentages = [round(pct, 2) for pct in percentages]

    for pct in percentages:

        # print(f"当前偏移度：{pct}")

        lp_problem_list = []
        for prd_comb in prod_comb_list:
            lp_problem_list.append({"bias": float(pct), "fof_portfolio_index": prd_comb})

        payload = {
            "fof_portfolio": fof_portfolio,
            "cust_hold_ratio": cust_hold_ratio,
            "cust_recommend_ratio": cust_rec_ratio,
            "lp_problem_list": lp_problem_list
        }

        payload_list.append(payload)

        # 单个请求顺序执行
        '''
        # print(f"传参：{payload}")
        print(f"传入组合个数：{len(lp_problem_list)}")
        
        start_time = time.time()
        res = requests.post(url, data=json.dumps(payload))
        # print(f"返回结果: {res.text}")

        res_json = res.json()
        print(f'有解组合数：{len(res_json.get("data"))}')
        print("耗时:", time.time() - start_time)

        print(f"=====================================================")
        '''

    # 模拟并行计算
    start_time = time.time()
    print(f"批次数：{len(payload_list)}")
    # print(payload_list)

    with ThreadPoolExecutor(max_workers=8) as executor:
        res_list = executor.map(send_post_request, percentages, [url] * len(payload_list), payload_list)
    
    # print(res_list)
    print("全部耗时:", time.time() - start_time)
