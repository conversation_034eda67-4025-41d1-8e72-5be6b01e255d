import uvicorn
from core import basic_config
from core.app import app
from api.v1.routers import all_routers
from fastapi.staticfiles import StaticFiles

# 注册路由
app.include_router(all_routers, prefix="/api/v1")

# 将静态文件托管到 /static
app.mount("/static", StaticFiles(directory=basic_config.STATIC_FILES_DIR), name="static")


if __name__ == "__main__":
    uvicorn.run(
        app,  # windows直接传递 app 对象而不是字符串，避免重复导入模块
        host=basic_config.WEB_HOST,
        port=basic_config.WEB_PORT,
        # workers=basic_config.WORKERS,
        workers=1,
        limit_concurrency=basic_config.LIMIT_CONCURRENCY,
        reload=False
    )
