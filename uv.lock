version = 1
revision = 3
requires-python = "==3.9.*"

[[package]]
name = "annotated-types"
version = "0.7.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/annotated-types/0.7.0/annotated_types-0.7.0.tar.gz", hash = "sha256:aff07c09a53a08bc8cfccb9c85b05f1aa9a2a6f23728d790723543408344ce89" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/annotated-types/0.7.0/annotated_types-0.7.0-py3-none-any.whl", hash = "sha256:1f02e8b43a8fbbc3f3e0d4f0f4bfc8131bcb4eebe8849b8e5c773f3a1c582a53" },
]

[[package]]
name = "anyio"
version = "4.10.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "exceptiongroup" },
    { name = "idna" },
    { name = "sniffio" },
    { name = "typing-extensions" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/anyio/4.10.0/anyio-4.10.0.tar.gz", hash = "sha256:3f3fae35c96039744587aa5b8371e7e8e603c0702999535961dd336026973ba6" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/anyio/4.10.0/anyio-4.10.0-py3-none-any.whl", hash = "sha256:60e474ac86736bbfd6f210f7a61218939c318f43f9972497381f1c5e930ed3d1" },
]

[[package]]
name = "certifi"
version = "2025.8.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/certifi/2025.8.3/certifi-2025.8.3.tar.gz", hash = "sha256:e564105f78ded564e3ae7c923924435e1daa7463faeab5bb932bc53ffae63407" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/certifi/2025.8.3/certifi-2025.8.3-py3-none-any.whl", hash = "sha256:f6c12493cfb1b06ba2ff328595af9350c65d6644968e5d3a2ffd78699af217a5" },
]

[[package]]
name = "charset-normalizer"
version = "3.4.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.3/charset_normalizer-3.4.3.tar.gz", hash = "sha256:6fce4b8500244f6fcb71465d4a4930d132ba9ab8e71a7859e6a5d59851068d14" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.3/charset_normalizer-3.4.3-cp39-cp39-macosx_10_9_universal2.whl", hash = "sha256:70bfc5f2c318afece2f5838ea5e4c3febada0be750fcf4775641052bbba14d05" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.3/charset_normalizer-3.4.3-cp39-cp39-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:23b6b24d74478dc833444cbd927c338349d6ae852ba53a0d02a2de1fce45b96e" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.3/charset_normalizer-3.4.3-cp39-cp39-manylinux2014_ppc64le.manylinux_2_17_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:34a7f768e3f985abdb42841e20e17b330ad3aaf4bb7e7aeeb73db2e70f077b99" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.3/charset_normalizer-3.4.3-cp39-cp39-manylinux2014_s390x.manylinux_2_17_s390x.manylinux_2_28_s390x.whl", hash = "sha256:fb731e5deb0c7ef82d698b0f4c5bb724633ee2a489401594c5c88b02e6cb15f7" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.3/charset_normalizer-3.4.3-cp39-cp39-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:257f26fed7d7ff59921b78244f3cd93ed2af1800ff048c33f624c87475819dd7" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.3/charset_normalizer-3.4.3-cp39-cp39-musllinux_1_2_aarch64.whl", hash = "sha256:1ef99f0456d3d46a50945c98de1774da86f8e992ab5c77865ea8b8195341fc19" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.3/charset_normalizer-3.4.3-cp39-cp39-musllinux_1_2_ppc64le.whl", hash = "sha256:2c322db9c8c89009a990ef07c3bcc9f011a3269bc06782f916cd3d9eed7c9312" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.3/charset_normalizer-3.4.3-cp39-cp39-musllinux_1_2_s390x.whl", hash = "sha256:511729f456829ef86ac41ca78c63a5cb55240ed23b4b737faca0eb1abb1c41bc" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.3/charset_normalizer-3.4.3-cp39-cp39-musllinux_1_2_x86_64.whl", hash = "sha256:88ab34806dea0671532d3f82d82b85e8fc23d7b2dd12fa837978dad9bb392a34" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.3/charset_normalizer-3.4.3-cp39-cp39-win32.whl", hash = "sha256:16a8770207946ac75703458e2c743631c79c59c5890c80011d536248f8eaa432" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.3/charset_normalizer-3.4.3-cp39-cp39-win_amd64.whl", hash = "sha256:d22dbedd33326a4a5190dd4fe9e9e693ef12160c77382d9e87919bce54f3d4ca" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.3/charset_normalizer-3.4.3-py3-none-any.whl", hash = "sha256:ce571ab16d890d23b5c278547ba694193a45011ff86a9162a71307ed9f86759a" },
]

[[package]]
name = "click"
version = "8.1.8"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/click/8.1.8/click-8.1.8.tar.gz", hash = "sha256:ed53c9d8990d83c2a27deae68e4ee337473f6330c040a31d4225c9574d16096a" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/click/8.1.8/click-8.1.8-py3-none-any.whl", hash = "sha256:63c132bbbed01578a06712a2d1f497bb62d9c1c0d329b7903a866228027263b2" },
]

[[package]]
name = "colorama"
version = "0.4.6"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/colorama/0.4.6/colorama-0.4.6.tar.gz", hash = "sha256:08695f5cb7ed6e0531a20572697297273c47b8cae5a63ffc6d6ed5c201be6e44" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/colorama/0.4.6/colorama-0.4.6-py2.py3-none-any.whl", hash = "sha256:4f1d9991f5acc0ca119f9d443620b77f9d6b33703e51011c16baf57afb285fc6" },
]

[[package]]
name = "et-xmlfile"
version = "2.0.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/et-xmlfile/2.0.0/et_xmlfile-2.0.0.tar.gz", hash = "sha256:dab3f4764309081ce75662649be815c4c9081e88f0837825f90fd28317d4da54" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/et-xmlfile/2.0.0/et_xmlfile-2.0.0-py3-none-any.whl", hash = "sha256:7a91720bc756843502c3b7504c77b8fe44217c85c537d85037f0f536151b2caa" },
]

[[package]]
name = "exceptiongroup"
version = "1.3.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/exceptiongroup/1.3.0/exceptiongroup-1.3.0.tar.gz", hash = "sha256:b241f5885f560bc56a59ee63ca4c6a8bfa46ae4ad651af316d4e81817bb9fd88" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/exceptiongroup/1.3.0/exceptiongroup-1.3.0-py3-none-any.whl", hash = "sha256:4d111e6e0c13d0644cad6ddaa7ed0261a0b36971f6d23e7ec9b4b9097da78a10" },
]

[[package]]
name = "fastapi"
version = "0.115.8"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "pydantic" },
    { name = "starlette" },
    { name = "typing-extensions" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/fastapi/0.115.8/fastapi-0.115.8.tar.gz", hash = "sha256:0ce9111231720190473e222cdf0f07f7206ad7e53ea02beb1d2dc36e2f0741e9" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/fastapi/0.115.8/fastapi-0.115.8-py3-none-any.whl", hash = "sha256:753a96dd7e036b34eeef8babdfcfe3f28ff79648f86551eb36bfc1b0bf4a8cbf" },
]

[[package]]
name = "h11"
version = "0.16.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/h11/0.16.0/h11-0.16.0.tar.gz", hash = "sha256:4e35b956cf45792e4caa5885e69fba00bdbc6ffafbfa020300e549b208ee5ff1" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/h11/0.16.0/h11-0.16.0-py3-none-any.whl", hash = "sha256:63cf8bbe7522de3bf65932fda1d9c2772064ffb3dae62d55932da54b31cb6c86" },
]

[[package]]
name = "idna"
version = "3.10"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/idna/3.10/idna-3.10.tar.gz", hash = "sha256:12f65c9b470abda6dc35cf8e63cc574b1c52b11df2c86030af0ac09b01b13ea9" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/idna/3.10/idna-3.10-py3-none-any.whl", hash = "sha256:946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3" },
]

[[package]]
name = "loguru"
version = "0.7.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
    { name = "win32-setctime", marker = "sys_platform == 'win32'" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/loguru/0.7.3/loguru-0.7.3.tar.gz", hash = "sha256:19480589e77d47b8d85b2c827ad95d49bf31b0dcde16593892eb51dd18706eb6" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/loguru/0.7.3/loguru-0.7.3-py3-none-any.whl", hash = "sha256:31a33c10c8e1e10422bfd431aeb5d351c7cf7fa671e3c4df004162264b28220c" },
]

[[package]]
name = "numpy"
version = "2.0.2"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/numpy/2.0.2/numpy-2.0.2.tar.gz", hash = "sha256:883c987dee1880e2a864ab0dc9892292582510604156762362d9326444636e78" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/numpy/2.0.2/numpy-2.0.2-cp39-cp39-macosx_10_9_x86_64.whl", hash = "sha256:9059e10581ce4093f735ed23f3b9d283b9d517ff46009ddd485f1747eb22653c" },
    { url = "http://pypi.howbuy.pa/packages/numpy/2.0.2/numpy-2.0.2-cp39-cp39-macosx_11_0_arm64.whl", hash = "sha256:423e89b23490805d2a5a96fe40ec507407b8ee786d66f7328be214f9679df6dd" },
    { url = "http://pypi.howbuy.pa/packages/numpy/2.0.2/numpy-2.0.2-cp39-cp39-macosx_14_0_arm64.whl", hash = "sha256:2b2955fa6f11907cf7a70dab0d0755159bca87755e831e47932367fc8f2f2d0b" },
    { url = "http://pypi.howbuy.pa/packages/numpy/2.0.2/numpy-2.0.2-cp39-cp39-macosx_14_0_x86_64.whl", hash = "sha256:97032a27bd9d8988b9a97a8c4d2c9f2c15a81f61e2f21404d7e8ef00cb5be729" },
    { url = "http://pypi.howbuy.pa/packages/numpy/2.0.2/numpy-2.0.2-cp39-cp39-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:1e795a8be3ddbac43274f18588329c72939870a16cae810c2b73461c40718ab1" },
    { url = "http://pypi.howbuy.pa/packages/numpy/2.0.2/numpy-2.0.2-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:f26b258c385842546006213344c50655ff1555a9338e2e5e02a0756dc3e803dd" },
    { url = "http://pypi.howbuy.pa/packages/numpy/2.0.2/numpy-2.0.2-cp39-cp39-musllinux_1_1_x86_64.whl", hash = "sha256:5fec9451a7789926bcf7c2b8d187292c9f93ea30284802a0ab3f5be8ab36865d" },
    { url = "http://pypi.howbuy.pa/packages/numpy/2.0.2/numpy-2.0.2-cp39-cp39-musllinux_1_2_aarch64.whl", hash = "sha256:9189427407d88ff25ecf8f12469d4d39d35bee1db5d39fc5c168c6f088a6956d" },
    { url = "http://pypi.howbuy.pa/packages/numpy/2.0.2/numpy-2.0.2-cp39-cp39-win32.whl", hash = "sha256:905d16e0c60200656500c95b6b8dca5d109e23cb24abc701d41c02d74c6b3afa" },
    { url = "http://pypi.howbuy.pa/packages/numpy/2.0.2/numpy-2.0.2-cp39-cp39-win_amd64.whl", hash = "sha256:a3f4ab0caa7f053f6797fcd4e1e25caee367db3112ef2b6ef82d749530768c73" },
    { url = "http://pypi.howbuy.pa/packages/numpy/2.0.2/numpy-2.0.2-pp39-pypy39_pp73-macosx_10_9_x86_64.whl", hash = "sha256:7f0a0c6f12e07fa94133c8a67404322845220c06a9e80e85999afe727f7438b8" },
    { url = "http://pypi.howbuy.pa/packages/numpy/2.0.2/numpy-2.0.2-pp39-pypy39_pp73-macosx_14_0_x86_64.whl", hash = "sha256:312950fdd060354350ed123c0e25a71327d3711584beaef30cdaa93320c392d4" },
    { url = "http://pypi.howbuy.pa/packages/numpy/2.0.2/numpy-2.0.2-pp39-pypy39_pp73-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:26df23238872200f63518dd2aa984cfca675d82469535dc7162dc2ee52d9dd5c" },
    { url = "http://pypi.howbuy.pa/packages/numpy/2.0.2/numpy-2.0.2-pp39-pypy39_pp73-win_amd64.whl", hash = "sha256:a46288ec55ebbd58947d31d72be2c63cbf839f0a63b49cb755022310792a3385" },
]

[[package]]
name = "openpyxl"
version = "3.1.5"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "et-xmlfile" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/openpyxl/3.1.5/openpyxl-3.1.5.tar.gz", hash = "sha256:cf0e3cf56142039133628b5acffe8ef0c12bc902d2aadd3e0fe5878dc08d1050" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/openpyxl/3.1.5/openpyxl-3.1.5-py2.py3-none-any.whl", hash = "sha256:5282c12b107bffeef825f4617dc029afaf41d0ea60823bbb665ef3079dc79de2" },
]

[[package]]
name = "packaging"
version = "25.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/packaging/25.0/packaging-25.0.tar.gz", hash = "sha256:d443872c98d677bf60f6a1f2f8c1cb748e8fe762d2bf9d3148b5599295b0fc4f" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/packaging/25.0/packaging-25.0-py3-none-any.whl", hash = "sha256:29572ef2b1f17581046b3a2227d5c611fb25ec70ca1ba8554b24b0e69331a484" },
]

[[package]]
name = "pandas"
version = "2.2.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "numpy" },
    { name = "python-dateutil" },
    { name = "pytz" },
    { name = "tzdata" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/pandas/2.2.3/pandas-2.2.3.tar.gz", hash = "sha256:4f18ba62b61d7e192368b84517265a99b4d7ee8912f8708660fb4a366cc82667" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pandas/2.2.3/pandas-2.2.3-cp39-cp39-macosx_10_9_x86_64.whl", hash = "sha256:bc6b93f9b966093cb0fd62ff1a7e4c09e6d546ad7c1de191767baffc57628f39" },
    { url = "http://pypi.howbuy.pa/packages/pandas/2.2.3/pandas-2.2.3-cp39-cp39-macosx_11_0_arm64.whl", hash = "sha256:5dbca4c1acd72e8eeef4753eeca07de9b1db4f398669d5994086f788a5d7cc30" },
    { url = "http://pypi.howbuy.pa/packages/pandas/2.2.3/pandas-2.2.3-cp39-cp39-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:8cd6d7cc958a3910f934ea8dbdf17b2364827bb4dafc38ce6eef6bb3d65ff09c" },
    { url = "http://pypi.howbuy.pa/packages/pandas/2.2.3/pandas-2.2.3-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:99df71520d25fade9db7c1076ac94eb994f4d2673ef2aa2e86ee039b6746d20c" },
    { url = "http://pypi.howbuy.pa/packages/pandas/2.2.3/pandas-2.2.3-cp39-cp39-musllinux_1_2_aarch64.whl", hash = "sha256:31d0ced62d4ea3e231a9f228366919a5ea0b07440d9d4dac345376fd8e1477ea" },
    { url = "http://pypi.howbuy.pa/packages/pandas/2.2.3/pandas-2.2.3-cp39-cp39-musllinux_1_2_x86_64.whl", hash = "sha256:7eee9e7cea6adf3e3d24e304ac6b8300646e2a5d1cd3a3c2abed9101b0846761" },
    { url = "http://pypi.howbuy.pa/packages/pandas/2.2.3/pandas-2.2.3-cp39-cp39-win_amd64.whl", hash = "sha256:4850ba03528b6dd51d6c5d273c46f183f39a9baf3f0143e566b89450965b105e" },
]

[[package]]
name = "pip"
version = "25.2"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/pip/25.2/pip-25.2.tar.gz", hash = "sha256:578283f006390f85bb6282dffb876454593d637f5d1be494b5202ce4877e71f2" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pip/25.2/pip-25.2-py3-none-any.whl", hash = "sha256:6d67a2b4e7f14d8b31b8b52648866fa717f45a1eb70e83002f4331d07e953717" },
]

[[package]]
name = "pipdeptree"
version = "2.25.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "packaging" },
    { name = "pip" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/pipdeptree/2.25.0/pipdeptree-2.25.0.tar.gz", hash = "sha256:029bcdcbd2e0130ec33b222c7833b8b5e52f674760dcf2df40b4ae6ff007a74f" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pipdeptree/2.25.0/pipdeptree-2.25.0-py3-none-any.whl", hash = "sha256:c1657cd0e143b6a17a496a97566daf1e29dc2ff5b87dc83f7e5589305fe92139" },
]

[[package]]
name = "pulp"
version = "2.9.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/pulp/2.9.0/pulp-2.9.0.tar.gz", hash = "sha256:2e30e6c0ef2c0edac185220e3e53faca62eb786a9bd68465208f05bc63e850f3" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pulp/2.9.0/PuLP-2.9.0-py3-none-any.whl", hash = "sha256:ad6a9b566d8458f4d05f4bfe2cea59e32885dd1da6929a361be579222107987c" },
]

[[package]]
name = "pydantic"
version = "2.10.6"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "annotated-types" },
    { name = "pydantic-core" },
    { name = "typing-extensions" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/pydantic/2.10.6/pydantic-2.10.6.tar.gz", hash = "sha256:ca5daa827cce33de7a42be142548b0096bf05a7e7b365aebfa5f8eeec7128236" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pydantic/2.10.6/pydantic-2.10.6-py3-none-any.whl", hash = "sha256:427d664bf0b8a2b34ff5dd0f5a18df00591adcee7198fbd71981054cef37b584" },
]

[[package]]
name = "pydantic-core"
version = "2.27.2"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2.tar.gz", hash = "sha256:eb026e5a4c1fee05726072337ff51d1efb6f59090b7da90d30ea58625b1ffb39" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-cp39-cp39-macosx_10_12_x86_64.whl", hash = "sha256:c10eb4f1659290b523af58fa7cffb452a61ad6ae5613404519aee4bfbf1df993" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-cp39-cp39-macosx_11_0_arm64.whl", hash = "sha256:ef592d4bad47296fb11f96cd7dc898b92e795032b4894dfb4076cfccd43a9308" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-cp39-cp39-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:c61709a844acc6bf0b7dce7daae75195a10aac96a596ea1b776996414791ede4" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-cp39-cp39-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:42c5f762659e47fdb7b16956c71598292f60a03aa92f8b6351504359dbdba6cf" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-cp39-cp39-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:4c9775e339e42e79ec99c441d9730fccf07414af63eac2f0e48e08fd38a64d76" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-cp39-cp39-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:57762139821c31847cfb2df63c12f725788bd9f04bc2fb392790959b8f70f118" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:0d1e85068e818c73e048fe28cfc769040bb1f475524f4745a5dc621f75ac7630" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-cp39-cp39-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:097830ed52fd9e427942ff3b9bc17fab52913b2f50f2880dc4a5611446606a54" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-cp39-cp39-musllinux_1_1_aarch64.whl", hash = "sha256:044a50963a614ecfae59bb1eaf7ea7efc4bc62f49ed594e18fa1e5d953c40e9f" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-cp39-cp39-musllinux_1_1_armv7l.whl", hash = "sha256:4e0b4220ba5b40d727c7f879eac379b822eee5d8fff418e9d3381ee45b3b0362" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-cp39-cp39-musllinux_1_1_x86_64.whl", hash = "sha256:5e4f4bb20d75e9325cc9696c6802657b58bc1dbbe3022f32cc2b2b632c3fbb96" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-cp39-cp39-win32.whl", hash = "sha256:cca63613e90d001b9f2f9a9ceb276c308bfa2a43fafb75c8031c4f66039e8c6e" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-cp39-cp39-win_amd64.whl", hash = "sha256:77d1bca19b0f7021b3a982e6f903dcd5b2b06076def36a652e3907f596e29f67" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-pp39-pypy39_pp73-macosx_10_12_x86_64.whl", hash = "sha256:c33939a82924da9ed65dab5a65d427205a73181d8098e79b6b426bdf8ad4e656" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-pp39-pypy39_pp73-macosx_11_0_arm64.whl", hash = "sha256:00bad2484fa6bda1e216e7345a798bd37c68fb2d97558edd584942aa41b7d278" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-pp39-pypy39_pp73-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:c817e2b40aba42bac6f457498dacabc568c3b7a986fc9ba7c8d9d260b71485fb" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-pp39-pypy39_pp73-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:251136cdad0cb722e93732cb45ca5299fb56e1344a833640bf93b2803f8d1bfd" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-pp39-pypy39_pp73-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:d2088237af596f0a524d3afc39ab3b036e8adb054ee57cbb1dcf8e09da5b29cc" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-pp39-pypy39_pp73-musllinux_1_1_aarch64.whl", hash = "sha256:d4041c0b966a84b4ae7a09832eb691a35aec90910cd2dbe7a208de59be77965b" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-pp39-pypy39_pp73-musllinux_1_1_armv7l.whl", hash = "sha256:8083d4e875ebe0b864ffef72a4304827015cff328a1be6e22cc850753bfb122b" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-pp39-pypy39_pp73-musllinux_1_1_x86_64.whl", hash = "sha256:f141ee28a0ad2123b6611b6ceff018039df17f32ada8b534e6aa039545a3efb2" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.27.2/pydantic_core-2.27.2-pp39-pypy39_pp73-win_amd64.whl", hash = "sha256:7d0c8399fcc1848491f00e0314bd59fb34a9c008761bcb422a057670c3f65e35" },
]

[[package]]
name = "pyds-fof"
version = "0.1.0"
source = { virtual = "." }
dependencies = [
    { name = "fastapi" },
    { name = "loguru" },
    { name = "openpyxl" },
    { name = "pandas" },
    { name = "pipdeptree" },
    { name = "pulp" },
    { name = "pydantic" },
    { name = "requests" },
    { name = "uvicorn" },
]

[package.metadata]
requires-dist = [
    { name = "fastapi", specifier = "==0.115.8" },
    { name = "loguru", specifier = "==0.7.3" },
    { name = "openpyxl", specifier = "==3.1.5" },
    { name = "pandas", specifier = "==2.2.3" },
    { name = "pipdeptree", specifier = "==2.25.0" },
    { name = "pulp", specifier = "==2.9.0" },
    { name = "pydantic", specifier = "==2.10.6" },
    { name = "requests", specifier = "==2.32.3" },
    { name = "uvicorn", specifier = "==0.34.0" },
]

[[package]]
name = "python-dateutil"
version = "2.9.0.post0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "six" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/python-dateutil/2.9.0.post0/python-dateutil-2.9.0.post0.tar.gz", hash = "sha256:37dd54208da7e1cd875388217d5e00ebd4179249f90fb72437e91a35459a0ad3" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/python-dateutil/2.9.0.post0/python_dateutil-2.9.0.post0-py2.py3-none-any.whl", hash = "sha256:a8b2bc7bffae282281c8140a97d3aa9c14da0b136dfe83f850eea9a5f7470427" },
]

[[package]]
name = "pytz"
version = "2025.2"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/pytz/2025.2/pytz-2025.2.tar.gz", hash = "sha256:360b9e3dbb49a209c21ad61809c7fb453643e048b38924c765813546746e81c3" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pytz/2025.2/pytz-2025.2-py2.py3-none-any.whl", hash = "sha256:5ddf76296dd8c44c26eb8f4b6f35488f3ccbf6fbbd7adee0b7262d43f0ec2f00" },
]

[[package]]
name = "requests"
version = "2.32.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "certifi" },
    { name = "charset-normalizer" },
    { name = "idna" },
    { name = "urllib3" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/requests/2.32.3/requests-2.32.3.tar.gz", hash = "sha256:55365417734eb18255590a9ff9eb97e9e1da868d4ccd6402399eaf68af20a760" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/requests/2.32.3/requests-2.32.3-py3-none-any.whl", hash = "sha256:70761cfe03c773ceb22aa2f671b4757976145175cdfca038c02654d061d6dcc6" },
]

[[package]]
name = "six"
version = "1.17.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/six/1.17.0/six-1.17.0.tar.gz", hash = "sha256:ff70335d468e7eb6ec65b95b99d3a2836546063f63acc5171de367e834932a81" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/six/1.17.0/six-1.17.0-py2.py3-none-any.whl", hash = "sha256:4721f391ed90541fddacab5acf947aa0d3dc7d27b2e1e8eda2be8970586c3274" },
]

[[package]]
name = "sniffio"
version = "1.3.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/sniffio/1.3.1/sniffio-1.3.1.tar.gz", hash = "sha256:f4324edc670a0f49750a81b895f35c3adb843cca46f0530f79fc1babb23789dc" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/sniffio/1.3.1/sniffio-1.3.1-py3-none-any.whl", hash = "sha256:2f6da418d1f1e0fddd844478f41680e794e6051915791a034ff65e5f100525a2" },
]

[[package]]
name = "starlette"
version = "0.45.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "anyio" },
    { name = "typing-extensions" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/starlette/0.45.3/starlette-0.45.3.tar.gz", hash = "sha256:2cbcba2a75806f8a41c722141486f37c28e30a0921c5f6fe4346cb0dcee1302f" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/starlette/0.45.3/starlette-0.45.3-py3-none-any.whl", hash = "sha256:dfb6d332576f136ec740296c7e8bb8c8a7125044e7c6da30744718880cdd059d" },
]

[[package]]
name = "typing-extensions"
version = "4.15.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/typing-extensions/4.15.0/typing_extensions-4.15.0.tar.gz", hash = "sha256:0cea48d173cc12fa28ecabc3b837ea3cf6f38c6d1136f85cbaaf598984861466" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/typing-extensions/4.15.0/typing_extensions-4.15.0-py3-none-any.whl", hash = "sha256:f0fa19c6845758ab08074a0cfa8b7aecb71c999ca73d62883bc25cc018c4e548" },
]

[[package]]
name = "tzdata"
version = "2025.2"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/tzdata/2025.2/tzdata-2025.2.tar.gz", hash = "sha256:b60a638fcc0daffadf82fe0f57e53d06bdec2f36c4df66280ae79bce6bd6f2b9" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/tzdata/2025.2/tzdata-2025.2-py2.py3-none-any.whl", hash = "sha256:1a403fada01ff9221ca8044d701868fa132215d84beb92242d9acd2147f667a8" },
]

[[package]]
name = "urllib3"
version = "2.5.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/urllib3/2.5.0/urllib3-2.5.0.tar.gz", hash = "sha256:3fc47733c7e419d4bc3f6b3dc2b4f890bb743906a30d56ba4a5bfa4bbff92760" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/urllib3/2.5.0/urllib3-2.5.0-py3-none-any.whl", hash = "sha256:e6b01673c0fa6a13e374b50871808eb3bf7046c4b125b216f6bf1cc604cff0dc" },
]

[[package]]
name = "uvicorn"
version = "0.34.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "click" },
    { name = "h11" },
    { name = "typing-extensions" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/uvicorn/0.34.0/uvicorn-0.34.0.tar.gz", hash = "sha256:404051050cd7e905de2c9a7e61790943440b3416f49cb409f965d9dcd0fa73e9" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/uvicorn/0.34.0/uvicorn-0.34.0-py3-none-any.whl", hash = "sha256:023dc038422502fa28a09c7a30bf2b6991512da7dcdb8fd35fe57cfc154126f4" },
]

[[package]]
name = "win32-setctime"
version = "1.2.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/win32-setctime/1.2.0/win32_setctime-1.2.0.tar.gz", hash = "sha256:ae1fdf948f5640aae05c511ade119313fb6a30d7eabe25fef9764dca5873c4c0" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/win32-setctime/1.2.0/win32_setctime-1.2.0-py3-none-any.whl", hash = "sha256:95d644c4e708aba81dc3704a116d8cbc974d70b3bdb8be1d150e36be6e9d1390" },
]

