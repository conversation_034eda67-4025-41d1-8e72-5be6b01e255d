from pydantic import BaseModel, Field, field_validator, model_validator
from typing import Optional, Union, List, Set
from enum import Enum


class StrategyType(str, Enum):
    """策略类型枚举"""
    SINGLE_STRATEGY = "singleStra"  # 单策略
    DUAL_STRATEGY = "doubleStra"    # 双拼策略
    MULTI_STRATEGY = "multiStra"    # 多策略


class RecommendProduct(BaseModel):
    """
    推荐产品池
    用于管理推荐产品的基本信息和资产配置比例
    """

    product_code: str = Field(
        description="产品代码",
        example="P00001"
    )

    product_type: str = Field(
        description="产品类型: fund-基金; zh-组合",
        example="fund"
    )


    stocks_domestic_proportion: float = Field(
        ge=0.0,
        le=1.0,
        description="国内股票占比",
        example=0.25
    )

    stocks_overseas_proportion: float = Field(
        ge=0.0,
        le=1.0,
        description="海外股票占比",
        example=0.15
    )

    bonds_domestic_proportion: float = Field(
        ge=0.0,
        le=1.0,
        description="国内债券占比",
        example=0.30
    )

    bonds_overseas_proportion: float = Field(
        ge=0.0,
        le=1.0,
        description="海外债券占比",
        example=0.10
    )

    commodity_proportion: float = Field(
        ge=0.0,
        le=1.0,
        description="商品占比",
        example=0.20
    )

    product_priority: str = Field(
        description="产品类型筛选优先级：1,2..., 数值越小优先级越高",
        example='1'
    )

    strategy_type: StrategyType = Field(
        description="策略类型：singleStra-单策略、doubleStra-双拼策略、multiStra-多策略",
        example=StrategyType.MULTI_STRATEGY
    )

    lower_amount_limit: float = Field(
        ge=0.0,
        description="单笔购买金额下限",
        example=1000.0
    )

    upper_amount_limit: float = Field(
        gt=0.0,
        description="单笔购买金额上限",
        example=10000000.0
    )

    @field_validator("product_code")
    @classmethod
    def validate_product_code(cls, v: str) -> str:
        """验证基金代码格式"""
        if not v or not v.strip():
            raise ValueError("基金代码不能为空")
        return v

    
    @model_validator(mode="after")
    def validate_proportions(self) -> "RecommendProduct":
        """验证各类资产占比总和"""
        total_proportion = (
            self.stocks_domestic_proportion +
            self.stocks_overseas_proportion +
            self.bonds_domestic_proportion +
            self.bonds_overseas_proportion +
            self.commodity_proportion
        )

        if abs(total_proportion-1.0) > 0.0001:
            raise ValueError(f"各类资产占比总和必须等于1.0，当前总和为: {total_proportion}")

        return self
    

    @model_validator(mode="after")
    def validate_amount_limits(self) -> "RecommendProduct":
        """验证金额限制"""
        if self.lower_amount_limit >= self.upper_amount_limit:
            raise ValueError("单笔购买金额下限必须小于上限")

        return self
    

    @property
    def data_dict(self) -> dict:
        """将模型数据转换为字典"""
        return {
            "product_code": self.product_code,
            "product_type": self.product_type,
            "stocks_domestic_proportion": self.stocks_domestic_proportion,
            "stocks_overseas_proportion": self.stocks_overseas_proportion,
            "bonds_domestic_proportion": self.bonds_domestic_proportion,
            "bonds_overseas_proportion": self.bonds_overseas_proportion,
            "commodity_proportion": self.commodity_proportion,
            "product_priority": self.product_priority,
            "strategy_type": self.strategy_type.value,
            "lower_amount_limit": self.lower_amount_limit,
            "upper_amount_limit": self.upper_amount_limit
        }


class CustPositionHolding(BaseModel):
    """
    客户当前持仓信息

    用于管理客户的当前持仓和推荐配置信息
    """

    hbone_no: str = Field(
        description="客户一账通号",
        example="87654321"
    )

    holding_market_cap: float = Field(
        ge=0.0,
        description="持仓市值(包含现金追加的金额)",
        example=1000000.0
    )
    
    holding_cash_amount: float = Field(
        gt=0.0,
        description="持有现金金额",
        example=100000.0
    )

    holding_cash_proportion: float = Field(
        ge=0.0,
        le=1.0,
        description="持有现金占比",
        example=0.10
    )

    holding_bonds_domestic_proportion: float = Field(
        ge=0.0,
        le=1.0,
        description="国内债券持有占比",
        example=0.30
    )

    holding_bonds_overseas_proportion: float = Field(
        ge=0.0,
        le=1.0,
        description="海外债券持有占比",
        example=0.10
    )


    holding_commodity_proportion: float = Field(
        ge=0.0,
        le=1.0,
        description="商品持有占比",
        example=0.05
    )


    holding_stocks_domestic_proportion: float = Field(
        ge=0.0,
        le=1.0,
        description="国内股票持有占比",
        example=0.25
    )

    holding_stocks_overseas_proportion: float = Field(
        ge=0.0,
        le=1.0,
        description="海外股票持有占比",
        example=0.20
    )

    recommend_bonds_domestic_proportion: float = Field(
        ge=0.0,
        le=1.0,
        description="国内债券推荐占比",
        example=0.35
    )

    recommend_bonds_overseas_proportion: float = Field(
        ge=0.0,
        le=1.0,
        description="海外债券推荐占比",
        example=0.15
    )

    recommend_commodity_proportion: float = Field(
        ge=0.0,
        le=1.0,
        description="商品推荐占比",
        example=0.10
    )

    recommend_stocks_domestic_proportion: float = Field(
        ge=0.0,
        le=1.0,
        description="国内股票推荐占比",
        example=0.25
    )

    recommend_stocks_overseas_proportion: float = Field(
        ge=0.0,
        le=1.0,
        description="海外股票推荐占比",
        example=0.15
    )

    @field_validator("hbone_no")
    @classmethod
    def validate_hbone_no(cls, v: str) -> str:
        """验证客户一账通号格式"""
        if not v or not v.strip():
            raise ValueError("客户一账通号不能为空")
        return v


    @model_validator(mode="after")
    def validate_holding_proportions(self) -> "CustPositionHolding":
        """验证当前持仓各类资产占比总和, 当前少了现金占比"""
        total_holding_proportion = (
            self.holding_stocks_domestic_proportion +
            self.holding_stocks_overseas_proportion +
            self.holding_bonds_domestic_proportion +
            self.holding_bonds_overseas_proportion +
            self.holding_commodity_proportion +
            self.holding_cash_proportion
        )

        # 允许一定的浮点数误差，持仓占比总和应该小于等于1.0
        if abs(total_holding_proportion-1.0) > 0.0001:
            raise ValueError(f"当前持仓各类资产占比总和必须等于1.0，当前总和为: {total_holding_proportion}")

        return self
    

    @model_validator(mode="after")
    def validate_recommend_proportions(self) -> "CustPositionHolding":
        """验证推荐配置各类资产占比总和"""
        total_recommend_proportion = (
            self.recommend_stocks_domestic_proportion +
            self.recommend_stocks_overseas_proportion +
            self.recommend_bonds_domestic_proportion +
            self.recommend_bonds_overseas_proportion +
            self.recommend_commodity_proportion
        )

        # 允许一定的浮点数误差，推荐占比总和应该等于1.0
        if abs(total_recommend_proportion - 1.0) > 0.0001:
            raise ValueError(f"推荐配置各类资产占比总和必须等于1.0，当前总和为: {total_recommend_proportion}")

        return self
    

    @property
    def data_dict(self) -> dict:
        """返回当前持仓信息字典"""
        return {
            "hbone_no": self.hbone_no,
            "holding_market_cap": self.holding_market_cap,
            "holding_cash_amount": self.holding_cash_amount,
            "holding_cash_proportion": self.holding_cash_proportion,
            "holding_bonds_domestic_proportion": self.holding_bonds_domestic_proportion,
            "holding_bonds_overseas_proportion": self.holding_bonds_overseas_proportion,
            "holding_stocks_domestic_proportion": self.holding_stocks_domestic_proportion,
            "holding_stocks_overseas_proportion": self.holding_stocks_overseas_proportion,
            "holding_commodity_proportion": self.holding_commodity_proportion,
            "recommend_bonds_domestic_proportion": self.recommend_bonds_domestic_proportion,
            "recommend_bonds_overseas_proportion": self.recommend_bonds_overseas_proportion,
            "recommend_stocks_domestic_proportion": self.recommend_stocks_domestic_proportion,
            "recommend_stocks_overseas_proportion": self.recommend_stocks_overseas_proportion,
            "recommend_commodity_proportion": self.recommend_commodity_proportion
        }


class CustAdjustPosition(BaseModel):
    """
    客户调仓

    用于管理客户调仓请求，包含客户当前持仓信息和推荐产品池
    """

    request_id: str = Field(
        description="请求id",
        example="a2d3e866-5235-11f0-a3f7-04bf1b344636"
    )

    cust_position_holding: CustPositionHolding = Field(
        description="客户当前持仓信息"
    )

    recommend_product_pool: List[RecommendProduct] = Field(
        description="推荐产品池",
        min_length=1
    )

    portfolio_list: List[List[str]] = Field(
        description="投资组合列表",
        example=[
            ["H00001"],
            ["P00001", "H00001"],
            ["P00001", "H00001", "P00002"]
        ]
    )

    @field_validator("request_id")
    @classmethod
    def validate_request_id(cls, v: str) -> str:
        """验证请求ID格式"""
        if not v or not v.strip():
            raise ValueError("请求ID不能为空")
        return v.strip()

    @field_validator("recommend_product_pool")
    @classmethod
    def validate_recommend_product_pool(cls, v: List[RecommendProduct]) -> List[RecommendProduct]:
        """验证推荐产品池"""
        if not v:
            raise ValueError("推荐产品池不能为空")

        # 检查基金代码是否重复
        product_codes = [product.product_code for product in v]
        if len(product_codes) != len(set(product_codes)):
            raise ValueError("推荐产品池中不能包含重复的基金代码")

        return v


    @property
    def product_count(self) -> int:
        """推荐产品数量"""
        return len(self.recommend_product_pool)

    @property
    def portfolio_count(self) -> int:
        """产品组合数量"""
        return len(self.portfolio_list)

    @property
    def data_dict(self) -> dict:
        """返回调仓信息字典"""
        return {
            "request_id": self.request_id,
            "cust_position_holding": self.cust_position_holding.data_dict,
            "recommend_product_pool": [product.data_dict for product in self.recommend_product_pool],
            "portfolio_list": self.portfolio_list
        }


class AdjustProductInfo(BaseModel):
    """
    给出的调仓方案:单个产品的调仓信息
    """

    product_code: str = Field(
        description="产品代码",
        example="H04066"
    )

    product_type: str = Field(
        description="产品类型",
        example="fund"
    )

    adjust_amount: float = Field(
        description="调仓金额",
        example=5000.0
    )

    @property
    def data_dict(self) -> dict:
        """返回调仓信息字典"""
        return {
            "product_code": self.product_code,
            "product_type": self.product_type,
            "adjust_amount": self.adjust_amount
        }


class CustLpOptimalSolution(BaseModel):
    """
    客户的线性优化调仓解决方案
    """

    hbone_no: str = Field(
        description="客户一账通号",
        example="87654321"
    )

    fund_num: int = Field(
        ge=0,
        description="基金数量",
        example=1
    )

    strategy_priority_min: int = Field(
        ge=0,
        description="策略优先级最小值",
        example=1
    )

    strategy_priority_sum: int = Field(
        ge=0,
        description="策略优先级求和",
        example=1
    )

    product_priority_min: int = Field(
        ge=0,
        description="产品优先级最小值",
        example=1
    )

    product_priority_sum: int = Field(
        ge=0,
        description="产品优先级求和",
        example=1
    )

    deviation_avg: float = Field(
        ge=0.0,
        description="偏离度均值",
        example=0.05
    )

    adjust_solution: List[AdjustProductInfo] = Field(
        description="调仓方案",
        min_length=0
    )

    @property
    def data_dict(self) -> dict:
        """返回调仓信息字典"""
        return {
            "hbone_no": self.hbone_no,
            "fund_num": self.fund_num,
            "strategy_priority_min": self.strategy_priority_min,
            "strategy_priority_sum": self.strategy_priority_sum,
            "product_priority_min": self.product_priority_min,
            "product_priority_sum": self.product_priority_sum,
            "deviation_avg": self.deviation_avg,
            "adjust_solution": [product.data_dict for product in self.adjust_solution]
        }


class CustLpOptimalSolutionList(BaseModel):
    """
    客户的线性优化调仓解决方案列表
    """
    request_id: str = Field(
        description="请求id",
        example="a2d3e866-5235-11f0-a3f7-04bf1b344636"
    )

    adjust_solutions: List[CustLpOptimalSolution] = Field(
        description="客户线性优化调仓解决方案列表",
        min_length=0
    )

    @property
    def data_dict(self) -> dict:
        """返回调仓信息字典"""
        return {
            "request_id": self.request_id,
            "adjust_solutions": [solution.data_dict for solution in self.adjust_solutions]
        }
