from pathlib import Path
import multiprocessing

BASE_DIR = Path(__file__).resolve().parent.parent.parent
# print(BASE_DIR)

#*****************************************************************************
#                               CPU利用
#*****************************************************************************

# 服务器cpu逻辑核数
CPU_COUNT = multiprocessing.cpu_count()

# 进程池
PROCESS_POOL_MAX_WORKERS = 2

#*****************************************************************************
#                               日志相关配置
#*****************************************************************************

LOGGING_LEVEL = "INFO"
LOGGING_BASE_DIR = Path(BASE_DIR, "logs")

#*****************************************************************************
#                               web相关配置
#*****************************************************************************
WEB_HOST = "0.0.0.0"
WEB_PORT = 8080
WORKERS = int(CPU_COUNT * 2 / PROCESS_POOL_MAX_WORKERS)
LIMIT_CONCURRENCY = CPU_COUNT * 2
STATIC_FILES_DIR = Path(BASE_DIR, "src", "static")
