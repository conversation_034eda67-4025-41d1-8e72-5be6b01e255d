import time
from models.retail import CustAdjustPosition, StrategyType, CustLpOptimalSolution, CustLpOptimalSolutionList
from core import basic_config
from loguru import logger as base_logger
from pathlib import Path
import pandas as pd
import asyncio
import pulp
import numpy as np
from core.app import run_in_process_pool
from typing import Optional, Union, List, Dict
import copy

log_dir = basic_config.LOGGING_BASE_DIR

# 创建专用于retail服务的logger实例，避免与fof服务的logger冲突
logger = base_logger.bind(service="retail")

log_format = (
    # 时间信息 + UUID
    "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | {extra[request_id]} | "
    # 日志级别，居中对齐
    "<level>{level: ^4}</level> | "
    # 进程和线程信息
    "process [<cyan>{process}</cyan>]:<cyan>{thread}</cyan> | "
    # 文件、函数和行号
    "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
    # 日志消息
    "<level>{message}</level>"
)

# 确保retail日志目录存在
retail_log_dir = Path(log_dir, "retail")
retail_log_dir.mkdir(parents=True, exist_ok=True)

# 添加retail专用的文件日志处理器（不移除现有处理器，保持控制台输出）
logger.add(
        str(Path(retail_log_dir, "retail_lp_solve.log")),
        format=log_format,
        level="INFO",
        rotation="10 MB",
        retention="365 days",
        encoding="utf-8",
        enqueue=True,  # 异步写入
        filter=lambda record: record.get("extra", {}).get("service") == "retail"
    )


class RetailFundAdjustService:

    def __init__(self, cust_adjust_position: CustAdjustPosition):
        self._params_preprocessing(cust_adjust_position)
    

    def _params_preprocessing(self, cust_adjust_position: CustAdjustPosition):
        """
        数据预处理
        :param cust_adjust_position: 客户调仓信息
        """

        start_time = time.time()
        self._request_id = cust_adjust_position.request_id

        with logger.contextualize(request_id=self._request_id):
            logger.info(f"数据预处理....")

            # 数据转为DataFrame类型
            data_dict = cust_adjust_position.data_dict

            # 格式转换
            self._df_cust_hold = pd.DataFrame([data_dict["cust_position_holding"]])
            self._df_product_pool = pd.DataFrame(data_dict["recommend_product_pool"]).astype({"product_priority": "int"}).set_index("product_code")
            self._portfolio_list = data_dict["portfolio_list"]

            logger.info(f"数据预处理完成, 耗时{time.time() - start_time}秒")

    def lp_solve(self, portfolio):
        """
        求解单个线性规划
        @param portfolio: 投资组合(基金代码列表, 如 ['P00001', 'P00002'])
        :return: 求解结果
        """

        start_time = time.time()

        with logger.contextualize(request_id=self._request_id):
            # logger.info("开始求解线性规划")
            
            # 从推荐产品池中取出产品信息
            df_portfolio_info = self._df_product_pool.loc[portfolio, :]

            # (1) 定义一个规划问题
            '''
            pulp.LpProblem 是定义问题的构造函数。
            "MyMinLPProblem" 是定义的问题名。
            参数 sense 用来指定求最小值/最大值问题，可选参数值：LpMinimize、LpMaximize
            '''
            MyProbLP = pulp.LpProblem("MyMinLPProblem", sense=pulp.LpMinimize)

            # (2) 定义决策变量
            '''
            pulp.LpVariable 是定义决策变量的函数。
            'x' 是用户定义的变量名。
            参数 lowBound、upBound 用来设定决策变量的下界、上界；可以不定义下界/上界，默认的下界/上界是负无穷/正无穷。
            参数 cat 用来设定变量类型，可选参数值：'Continuous' 表示连续变量（默认值）、'Integer' 表示离散变量（用于整数规划问题）、'Binary' 表示0/1变量（用于0/1规划问题）。
            '''

            # x的单位是1元
            x = pulp.LpVariable.dicts("x", range(len(portfolio)), lowBound=1, upBound=None, cat='Continuous')
            # y是偏离度, y ∈ [0, 0.05]
            percentage = pulp.LpVariable("percentage", lowBound=0, upBound=0.05, cat='Continuous')


            # (3) 设置目标函数，偏离度最小
            '''
            添加目标函数使用 “问题名 += 目标函数式” 格式。
            '''
            MyProbLP += percentage

            # (4) 添加约束条件
            '''
            添加约束条件使用 “问题名 += 约束条件表达式” 格式。
            约束条件可以是等式约束或不等式约束，不等式约束可以是 小于等于 或 大于等于，分别使用关键字">="、"<=“和”=="。
            '''

            # 加仓金额要大于初始值
            for idx, val in enumerate(portfolio):
                # 购买金额大于起购金额和加购金额
                MyProbLP += (x[idx] >= df_portfolio_info.loc[val, "lower_amount_limit"])
                # 购买金额要小于产品限额
                MyProbLP += (x[idx] <= df_portfolio_info.loc[val, "upper_amount_limit"])

            # 限制总金额（等式条件）： 各个产品的金额之和要等于现金
            MyProbLP += (pulp.lpSum([x[i] for i in range(len(portfolio))])) == self._df_cust_hold.iloc[0]["holding_market_cap"]*self._df_cust_hold.iloc[0]["holding_cash_proportion"]


            # 调仓后的各资产类别的占比与推荐占比的偏离度在0.05以内
            # 列表中的每一个元祖表示：( 资产类别的当前占比, 资产类别的推荐占比, 基金产品中某资产类别占比)
            asset_type_fields = [
                ("holding_stocks_domestic_proportion", "recommend_stocks_domestic_proportion", "stocks_domestic_proportion"),
                ("holding_stocks_overseas_proportion", "recommend_stocks_overseas_proportion", "stocks_overseas_proportion"),
                ("holding_bonds_domestic_proportion", "recommend_bonds_domestic_proportion", "bonds_domestic_proportion"),
                ("holding_bonds_overseas_proportion", "recommend_bonds_overseas_proportion", "bonds_overseas_proportion"),
                ("holding_commodity_proportion", "recommend_commodity_proportion", "commodity_proportion")
            ]
            
            for _hold_prop, _rec_prop, _prd_prop in asset_type_fields:
                new_amt = pulp.lpSum(x[i] * df_portfolio_info.iloc[i][_prd_prop] for i in range(len(portfolio))) + \
                    self._df_cust_hold.iloc[0]["holding_market_cap"]*self._df_cust_hold.iloc[0][_hold_prop]
                
                rec_amt_up = self._df_cust_hold.iloc[0]["holding_market_cap"]*(self._df_cust_hold.iloc[0][_rec_prop] + percentage)
                rec_amt_low = self._df_cust_hold.iloc[0]["holding_market_cap"]*(self._df_cust_hold.iloc[0][_rec_prop] - percentage)

                MyProbLP += (new_amt <= rec_amt_up)
                MyProbLP += (new_amt >= rec_amt_low)
            
            # (5) 求解

            MyProbLP.solve(pulp.PULP_CBC_CMD(msg=0))

            if pulp.LpStatus[MyProbLP.status] == 'Optimal':
                solve_result = {
                     "products": portfolio,
                     "amounts": [pulp.value(x[i]) for i in x],
                     "percentage": pulp.value(percentage)
                }
                
                cost_time = time.time() - start_time
                logger.info(f"求解问题=【{portfolio}】，求解结果=【{solve_result}】，lp求解耗时{cost_time}秒")
                return solve_result
            else:
                cost_time = time.time() - start_time
                logger.info(f"求解问题=【{portfolio}】，求解结果=【无解】，求解耗时{cost_time}秒")
                return None

    def select_optimal_portfolio(self, lp_result_list):
        """
        从多个lp有解的方案中，按照规则选择最终的一个方案
        """

        df_lp_result = pd.DataFrame(lp_result_list)

        # 深拷贝，防止修改原始数据
        df_cust_hold = self._df_cust_hold.copy()

        # 拆成多行
        df_lp_result_explode = df_lp_result.explode(['products', 'amounts'])
        # 将explode之前的index设置成comb_index，方便后面进行group by
        df_lp_result_explode['comb_index'] = df_lp_result_explode.index
        # 设置类型
        df_lp_result_explode['amounts'] = df_lp_result_explode['amounts'].astype(np.float64)

        # 匹配产品池中产品信息
        df_prd_adjust = pd.merge(self._df_product_pool, df_lp_result_explode, left_index=True, right_on='products')

        # 计算加仓产品每个资产类别的金额
        df_prd_adjust.eval('''
            stocks_domestic_adjust_amounts = amounts * stocks_domestic_proportion
            stocks_overseas_adjust_amounts = amounts * stocks_overseas_proportion
            bonds_domestic_adjust_amounts = amounts * bonds_overseas_proportion
            bonds_overseas_adjust_amounts = amounts * bonds_overseas_proportion
            commodity_adjust_amounts = amounts * commodity_proportion
            ''', inplace=True)

        # 计算策略优先级: 三拼>双拼>单拼
        conditions = [
            df_prd_adjust['strategy_type'] == StrategyType.MULTI_STRATEGY,
            df_prd_adjust['strategy_type'] == StrategyType.DUAL_STRATEGY,
            df_prd_adjust['strategy_type'] == StrategyType.SINGLE_STRATEGY
        ]

        choices = [1, 2, 3]
        df_prd_adjust['strategy_priority'] = np.select(conditions, choices)


        # 计算加仓方案中每个资产类别的金额
        df_asset_new = df_prd_adjust.groupby(['comb_index']).agg(
            adjust_amounts=('amounts', 'sum'),
            stocks_domestic_adjust_amounts=('stocks_domestic_adjust_amounts', 'sum'),
            stocks_overseas_adjust_amounts=('stocks_overseas_adjust_amounts', 'sum'),
            bonds_domestic_adjust_amounts=('bonds_domestic_adjust_amounts', 'sum'),
            bonds_overseas_adjust_amounts=('bonds_overseas_adjust_amounts', 'sum'),
            commodity_adjust_amounts=('commodity_adjust_amounts', 'sum'),
            fund_num=('comb_index', 'count'),
            strategy_priority_min=('strategy_priority', 'min'),
            strategy_priority_sum=('strategy_priority', 'sum'),
            product_priority_min=('product_priority', 'min'),
            product_priority_sum=('product_priority', 'sum'),
            percentage=('percentage','mean')
        )

        # 保留原来的comb_index
        df_asset_new ["gid"] = df_asset_new.index

        # 和调仓前的客户持仓进行合并
        df_result = df_cust_hold.assign(key=1).merge(df_asset_new.assign(key=1), on='key').drop('key', axis=1)

        # 计算新的资产类别占比、资产类别的偏离度之和
        df_result.eval('''
            new_stocks_domestic_proportion = (holding_market_cap * holding_stocks_domestic_proportion + stocks_domestic_adjust_amounts) / holding_market_cap
            new_stocks_overseas_proportion = (holding_market_cap * holding_stocks_overseas_proportion + stocks_overseas_adjust_amounts) / holding_market_cap
            new_bonds_domestic_proportion = (holding_market_cap * holding_bonds_domestic_proportion + bonds_domestic_adjust_amounts) / holding_market_cap
            new_bonds_overseas_proportion = (holding_market_cap * holding_bonds_overseas_proportion + bonds_overseas_adjust_amounts) / holding_market_cap
            new_commodity_proportion = (holding_market_cap * holding_commodity_proportion + commodity_adjust_amounts) / holding_market_cap
            stocks_domestic_deviation = abs(new_stocks_domestic_proportion - recommend_stocks_domestic_proportion)
            stocks_overseas_deviation = abs(new_stocks_overseas_proportion - recommend_stocks_overseas_proportion)
            bonds_domestic_deviation = abs(new_bonds_domestic_proportion - recommend_bonds_domestic_proportion)
            bonds_overseas_deviation = abs(new_bonds_overseas_proportion - recommend_bonds_overseas_proportion)
            commodity_deviation = abs(new_commodity_proportion - recommend_commodity_proportion)
            deviation_sum = stocks_domestic_deviation + stocks_overseas_deviation + bonds_domestic_deviation + bonds_overseas_deviation + commodity_deviation 
            deviation_avg = deviation_sum / 5.0              
            ''', inplace=True)
    
        
        # 默认推荐逻辑，按如下逻辑取最终方案，以下逻辑为递进关系：
        # 1、首先，优先产品数少的，一个 > 两个 > 三个
        # 2、其次，优先有三拼策略的，三拼 > 双拼 > 单拼
        # 3、根据产品优先级，再筛选一道：
        #   三拼：全球配置宝 > 国内大类配置
        #   单拼：全A增强 > 牛基宝全股型（举例，单拼优先级待定）
        # 4、最后，按照偏离度均值从小到大排序，选取偏离度均值最小的方案

        df_sorted = df_result.sort_values(
            by=['hbone_no', 'fund_num', 'strategy_priority_min', 'strategy_priority_sum', 'product_priority_min', 'product_priority_sum', 'deviation_avg'], 
            ascending=[True, True, True, True, True, True, True],
            kind='mergesort'
        )

        # 取第一个
        df_optimal_portfolio = df_prd_adjust.loc[df_prd_adjust["comb_index"]==df_sorted.iloc[0]["gid"], ["products", "amounts", "product_type"]].rename(
            columns={"products": "product_code", "amounts": "adjust_amount"}).astype({'product_code': 'str', 'product_type': 'str', 'adjust_amount': 'float'})

        return { "hbone_no": df_sorted.iloc[0]["hbone_no"],
                 "fund_num": int(df_sorted.iloc[0]["fund_num"]),
                 "strategy_priority_min": int(df_sorted.iloc[0]["strategy_priority_min"]),
                 "strategy_priority_sum": int(df_sorted.iloc[0]["strategy_priority_sum"]),
                 "product_priority_min": int(df_sorted.iloc[0]["product_priority_min"]),
                 "product_priority_sum": int(df_sorted.iloc[0]["product_priority_sum"]),
                 "deviation_avg": float(df_sorted.iloc[0]["deviation_avg"]),
                 "adjust_solution": df_optimal_portfolio.to_dict(orient='records')
                }


    async def multi_process_lp_solve(self):
        """
        多进程求解多个线性规划
        :return: 优化结果
        """
        with logger.contextualize(request_id=self._request_id):
            logger.info(f"多进程求解LP问题开始...")
            tasks = [run_in_process_pool(self.lp_solve, portfolio) for portfolio in self._portfolio_list]
            logger.info(f"多进程求解，任务数={len(tasks)}")
            solve_result_list = await asyncio.gather(*tasks)
            solve_result_list = [result for result in solve_result_list if result is not None]
            logger.info(f"多进程求解LP问题结束，有解数={len(solve_result_list)}")

            if len(solve_result_list) == 0:
               optimal_portfolio = None
            else:
                optimal_portfolio = self.select_optimal_portfolio(solve_result_list)
                logger.info(f"按优先级排序选择最终方案完成，输出结果返回：{optimal_portfolio}")
            
            return optimal_portfolio



def getGlobalOptimalSolution(adjust_solutions: List[Dict]):
    """
    从多个LP有解的方案中选择全局最优解。

    Args:
        solution_list: 包含多个客户LP最优解的列表，每个解是一个字典
        
    Returns:
        dict: 按照排序规则选择出的最优解
        
    排序规则:
        1. 按hbone_no升序
        2. 按fund_num升序 
        3. 按strategy_priority_min升序
        4. 按strategy_priority_sum升序
        5. 按product_priority_min升序
        6. 按product_priority_sum升序
        7. 按deviation_avg升序
    """

    copy_adjust_solutions = []

    for i, solution in enumerate(adjust_solutions):
        solution_copy = copy.deepcopy(solution)
        solution_copy['id'] = i
        del solution_copy['adjust_solution']
        copy_adjust_solutions.append(solution_copy)
    
    df_solution = pd.DataFrame(copy_adjust_solutions)

    df_sorted = df_solution.sort_values(
        by=['hbone_no', 'fund_num', 'strategy_priority_min', 'strategy_priority_sum', 'product_priority_min', 'product_priority_sum', 'deviation_avg'], 
        ascending=[True, True, True, True, True, True, True],
        kind='mergesort'
    )

    # 取第一个
    return adjust_solutions[df_sorted.iloc[0]["id"]]

