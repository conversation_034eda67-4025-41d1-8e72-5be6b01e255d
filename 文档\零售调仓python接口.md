
# 零售调仓

Base URLs:

* <a href="http://192.168.211.119:8000">测试环境: http://192.168.211.119:8000</a>

## POST 零售调仓线性规划求解

POST /api/v1/retail/adjust/lpsolve


### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» request_id|body|string| 是 | 请求id|none|
|» recommend_product_pool|body|[object]| 是 | 推荐产品池|none|
|»» product_code|body|string| 是 | 产品代码|none|
|»» product_type|body|string| 是 | 产品类型|fund-基金； zh-组合|
|»» stocks_domestic_proportion|body|number| 是 | 国内股票占比|none|
|»» stocks_overseas_proportion|body|number| 是 | 海外股票占比|none|
|»» bonds_domestic_proportion|body|number| 是 | 国内债券占比|none|
|»» bonds_overseas_proportion|body|number| 是 | 海外债券占比|none|
|»» commodity_proportion|body|number| 是 | 商品占比|none|
|»» lower_amount_limit|body|number| 是 | 单笔购买金额下限|none|
|»» upper_amount_limit|body|number| 是 | 单笔购买金额上限|none|
|»» strategy_type|body|string| 是 | 策略类型|singleStra-单策略、doubleStra-双拼策略、multiStra多策略|
|»» product_priority|body|string| 是 | 产品类型|策略内的产品优先级：1、2 （数值越小，优先级越高）|
|» cust_position_holding|body|object| 是 | 客户当前持仓|none|
|»» hbone_no|body|string| 是 | 客户一账通号|none|
|»» holding_market_cap|body|number| 是 | 持仓市值|none|
|»» holding_cash_amount|body|number| 是 | 持有现金金额|none|
|»» holding_cash_proportion|body|number| 是 | 持有现金占比|none|
|»» holding_stocks_domestic_proportion|body|number| 是 | 国内股票持有占比|none|
|»» holding_stocks_overseas_proportion|body|number| 是 | 海外股票持有占比|none|
|»» holding_bonds_domestic_proportion|body|number| 是 | 国内债券持有占比|none|
|»» holding_bonds_overseas_proportion|body|number| 是 | 海外债券持有占比|none|
|»» holding_commodity_proportion|body|number| 是 | 商品持有占比|none|
|»» recommend_stocks_domestic_proportion|body|number| 是 | 国内股票推荐占比	|none|
|»» recommend_stocks_overseas_proportion|body|number| 是 | 海外股票推荐占比	|none|
|»» recommend_bonds_domestic_proportion|body|number| 是 | 国内债券推荐占比	|none|
|»» recommend_bonds_overseas_proportion|body|number| 是 | 海外债券推荐占比	|none|
|»» recommend_commodity_proportion|body|number| 是 | 商品推荐占比|none|
|» portfolio_list|body|[array]| 是 | 投资组合列表|none|


### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|返回代码|0-成功;  9999-失败|
|» message|string|true|none|返回消息|none|
|» data|object|true|none|成功时返回数据|none|
|»» hbone_no|string|true|none|客户一账通号|none|
|»» fund_num|integer|true|none|基金数量|none|
|»» strategy_priority_min|integer|true|none|策略优先级最小值|none|
|»» strategy_priority_sum|integer|true|none|策略优先级求和|none|
|»» product_priority_min|integer|true|none|产品优先级最小值|none|
|»» product_priority_sum|integer|true|none|产品优先级求和|none|
|»» deviation_avg|number|true|none|偏离度均值|none|
|»» adjust_solution|[object]|true|none|调仓方案|none|
|»»» product_code|string|true|none|产品代码|none|
|»»» adjust_amount|integer|true|none|调仓金额|none|
|»»» product_type|string|true|none|产品类型|none|


## POST 零售调仓获取全局最优

POST /api/v1/retail/adjust/global-optima

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» request_id|body|string| 是 | 请求id|none|
|» adjust_solutions|body|[object]| 是 ||none|
|»» hbone_no|body|string| 是 | 一账通号|none|
|»» fund_num|body|integer| 是 | 基金数量|none|
|»» strategy_priority_min|body|integer| 是 | 策略优先级最小值|none|
|»» strategy_priority_sum|body|integer| 是 | 策略优先级求和|none|
|»» product_priority_min|body|integer| 是 | 产品优先级最小值|none|
|»» product_priority_sum|body|integer| 是 | 产品优先级求和|none|
|»» deviation_avg|body|integer| 是 | 偏离度均值|none|
|»» adjust_solution|body|[object]| 是 | 调仓方案|none|
|»»» product_code|body|string| 是 | 产品代码|none|
|»»» product_type|body|string| 是 | 产品类型|none|
|»»» adjust_amount|body|number| 是 | 调仓金额|none|


### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|string|true|none|返回代码|0-成功;  9999-失败|
|» message|string|true|none|返回消息|none|
|» data|object|true|none|成功时返回数据|none|
|»» hbone_no|string|true|none|客户一账通号|none|
|»» fund_num|string|true|none|基金数量|none|
|»» strategy_priority_min|string|true|none|策略优先级最小值|none|
|»» strategy_priority_sum|string|true|none|策略优先级求和|none|
|»» product_priority_min|string|true|none|产品优先级最小值|none|
|»» product_priority_sum|string|true|none|产品优先级求和|none|
|»» deviation_avg|number|true|none|偏离度均值|none|
|»» adjust_solution|[object]|true|none|调仓方案|none|
|»»» product_code|string|true|none|产品代码|none|
|»»» product_type|string|true|none|产品类型|none|
|»»» adjust_amount|integer|true|none|调仓金额|none|




