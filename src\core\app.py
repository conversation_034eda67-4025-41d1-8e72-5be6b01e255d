
import asyncio
from fastapi import <PERSON><PERSON><PERSON>
from functools import partial
from concurrent.futures import <PERSON><PERSON>ool<PERSON>xecutor
from contextlib import asynccontextmanager
from fastapi.openapi.docs import get_swagger_ui_html
from core import basic_config
from utils import logger as log_util
from loguru import logger

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动逻辑
    log_util.setup_logging()
    app.state.executor = ProcessPoolExecutor(max_workers=basic_config.PROCESS_POOL_MAX_WORKERS)
    logger.info("ProcessPoolExecutor initialized.")
    yield
    # 关闭逻辑
    app.state.executor.shutdown(wait=True)
    logger.info("ProcessPoolExecutor shutdown.")


app = FastAPI(lifespan=lifespan)

# 自定义 Swagger UI 路由
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url="/openapi.json",
        title="API Docs",
        swagger_js_url="/static/swagger-ui-bundle.js",  # 本地路径
        swagger_css_url="/static/swagger-ui.css"
    )

async def run_in_process_pool(fn, *args, timeout: int = 600, **kwargs): 
    loop = asyncio.get_event_loop() 
    future = loop.run_in_executor(app.state.executor, partial(fn, *args, **kwargs)) 
    try: 
        return await asyncio.wait_for(future, timeout=timeout) 
    except TimeoutError: 
        logger.error(f"Task timed out after {timeout} seconds -- task: {fn.__name__}, args: {args}, kwargs: {kwargs}.") 
        raise Exception(f"Task execution exceeded timeout of {timeout} seconds -- task: {fn.__name__}, args: {args}, kwargs: {kwargs}.")
