"""
零售基金调仓API功能测试
根据需求.md中2. 功能测试部分实现
"""

import sys
import os
import uuid
import pandas as pd
import requests
import json
import itertools
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from models.retail import CustPositionHolding, RecommendProduct, CustAdjustPosition, StrategyType


class TestRetailAdjustAPI:
    """零售基金调仓API功能测试类"""
    
    def __init__(self):
        self.base_url = "http://127.0.0.1:8080"  # 使用8080端口
        self.test_data_dir = Path(__file__).parent
        
    def load_cust_hold_data(self) -> CustPositionHolding:
        """
        (1) 读取src\tests\test_retail\cust_hold.csv文件，生成CustPositionHolding对象
        """
        csv_file = self.test_data_dir / "cust_hold.csv"
        
        if not csv_file.exists():
            raise FileNotFoundError(f"测试数据文件不存在: {csv_file}")
            
        df = pd.read_csv(csv_file, dtype={'hbone_no': 'str'})
        
        if df.empty:
            raise ValueError("客户持仓数据文件为空")
            
        # 取第一行数据
        row = df.iloc[0]
        
        cust_position = CustPositionHolding(
            hbone_no=str(row['hbone_no']),
            holding_market_cap=float(row['holding_market_cap']),
            holding_stocks_domestic_proportion=float(row['holding_stocks_domestic_proportion']),
            holding_bonds_domestic_proportion=float(row['holding_bonds_domestic_proportion']),
            holding_stocks_overseas_proportion=float(row['holding_stocks_overseas_proportion']),
            holding_bonds_overseas_proportion=float(row['holding_bonds_overseas_proportion']),
            holding_commodity_proportion=float(row['holding_commodity_proportion']),
            holding_cash_proportion=float(row['holding_cash_proportion']),
            holding_cash_amount=float(row['holding_cash_amount']),
            recommend_stocks_domestic_proportion=float(row['recommend_stocks_domestic_proportion']),
            recommend_bonds_domestic_proportion=float(row['recommend_bonds_domestic_proportion']),
            recommend_stocks_overseas_proportion=float(row['recommend_stocks_overseas_proportion']),
            recommend_bonds_overseas_proportion=float(row['recommend_bonds_overseas_proportion']),
            recommend_commodity_proportion=float(row['recommend_commodity_proportion'])
        )
        
        print(f"✅ 成功加载客户持仓数据: {cust_position.hbone_no}")
        return cust_position
        
    def load_recommend_product_pool(self) -> list[RecommendProduct]:
        """
        (2) 读取src\tests\test_retail\recommend_product_pool.csv文件，生成RecommendProduct对象
        """
        csv_file = self.test_data_dir / "recommend_product_pool.csv"
        
        if not csv_file.exists():
            raise FileNotFoundError(f"测试数据文件不存在: {csv_file}")
            
        df = pd.read_csv(csv_file)
        
        if df.empty:
            raise ValueError("推荐产品池数据文件为空")
            
        products = []
        for _, row in df.iterrows():
            product = RecommendProduct(
                product_code=str(row['product_code']).strip(),
                product_type=str(row['product_type']).strip(),
                bonds_domestic_proportion=float(row['bonds_domestic_proportion']),
                bonds_overseas_proportion=float(row['bonds_overseas_proportion']),
                stocks_domestic_proportion=float(row['stocks_domestic_proportion']),
                stocks_overseas_proportion=float(row['stocks_overseas_proportion']),
                commodity_proportion=float(row['commodity_proportion']),
                lower_amount_limit=float(row['lower_amount_limit']),
                upper_amount_limit=float(row['upper_amount_limit']),
                strategy_type=StrategyType(str(row['strategy_type']).strip()),
                product_priority=str(row['product_priority']).strip()
            )
            products.append(product)
            
        print(f"✅ 成功加载推荐产品池数据: {len(products)} 个产品")
        return products
        
    def generate_portfolio_list(self, products: list[RecommendProduct]) -> list[list[str]]:
        """
        (3) 根据步骤2中生成的RecommendProduct对象, 当前仅每次取1个product_code，生成投资组合列表
        """

        # 初始化一个空列表来存储要剔除的组合
        multiStra_qqpzb_list=[]  # 三拼: 全球配置宝
        multiStra_gndlpz_list=[] # 三拼: 国内大类配置
        all_product_list = []

        # 要剔除的组合情况，包含这些组合的都要剔除
        for product in products:
            if product.product_priority == '1' and product.strategy_type == 'multiStra':
                multiStra_qqpzb_list.append(product.product_code)

            if product.product_priority == '2' and product.strategy_type == 'multiStra':
                multiStra_gndlpz_list.append(product.product_code)

            all_product_list.append(product.product_code)

        exclude_list= list(itertools.combinations(multiStra_qqpzb_list, 2)) + list(itertools.combinations(multiStra_gndlpz_list, 2))

        portfolio_list = []
        
        # # 每次取1个product_code生成投资组合
        # for product in products:
        #     portfolio_list.append([product.product_code])

        product_combinations = list(itertools.combinations(all_product_list, 3))
        # 将组合添加到列表中
        for comb in product_combinations:
            if any(set(sub).issubset(set(comb)) for sub in exclude_list) == False:
                portfolio_list.append(comb)

            
        print(f"✅ 成功生成投资组合列表: {len(portfolio_list)} 个组合")
        return portfolio_list
        
    def create_cust_adjust_position(self, 
                                   cust_position: CustPositionHolding,
                                   products: list[RecommendProduct],
                                   portfolio_list: list[list[str]]) -> CustAdjustPosition:
        """
        (4) CustPositionHolding, RecommendProduct, portfolio_list 生成CustAdjustPosition对象
        """
        request_id = str(uuid.uuid4())
        
        cust_adjust_position = CustAdjustPosition(
            request_id=request_id,
            cust_position_holding=cust_position,
            recommend_product_pool=products,
            portfolio_list=portfolio_list
        )
        
        print(f"✅ 成功创建客户调仓对象: {request_id}")
        return cust_adjust_position
        
    def call_lpsolve_api(self, cust_adjust_position: CustAdjustPosition) -> dict:
        """
        (5) post请求调用接口http://127.0.0.1:8000/api/v1/retail/adjust/lpsolve
        """
        url = f"{self.base_url}/api/v1/retail/adjust/lpsolve"
        
        # 将对象转换为JSON字符串
        json_data = cust_adjust_position.model_dump()
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        try:
            print(f"🚀 发送POST请求到: {url}")
            print(f"📦 请求数据: 客户号={cust_adjust_position.cust_position_holding.hbone_no}, "
                  f"产品数量={len(cust_adjust_position.recommend_product_pool)}, "
                  f"组合数量={len(cust_adjust_position.portfolio_list)}")
            
            response = requests.post(url, json=json_data, headers=headers, timeout=60)
            
            print(f"📡 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ API调用成功")
                print(f"📊 响应结果: code={result.get('code')}, message={result.get('message')}")
                if 'cost_time' in result:
                    print(f"⏱️  耗时: {result['cost_time']:.2f}秒")
                return result
            else:
                print(f"❌ API调用失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return {"error": f"HTTP {response.status_code}", "message": response.text}
                
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
            return {"error": "timeout", "message": "请求超时"}
        except requests.exceptions.ConnectionError:
            print("❌ 连接错误，请确保服务器正在运行")
            return {"error": "connection_error", "message": "无法连接到服务器"}
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return {"error": "exception", "message": str(e)}
            
    def run_test(self):
        """运行完整的功能测试"""
        print("=" * 60)
        print("🧪 开始零售基金调仓API功能测试")
        print("=" * 60)
        
        try:
            # 步骤1: 加载客户持仓数据
            print("\n📋 步骤1: 加载客户持仓数据")
            cust_position = self.load_cust_hold_data()
            
            # 步骤2: 加载推荐产品池数据
            print("\n📋 步骤2: 加载推荐产品池数据")
            products = self.load_recommend_product_pool()
            
            # 步骤3: 生成投资组合列表
            print("\n📋 步骤3: 生成投资组合列表")
            portfolio_list = self.generate_portfolio_list(products)
            
            # 步骤4: 创建客户调仓对象
            print("\n📋 步骤4: 创建客户调仓对象")
            cust_adjust_position = self.create_cust_adjust_position(cust_position, products, portfolio_list)
            
            # 步骤5: 调用API
            print("\n📋 步骤5: 调用线性规划求解API")
            result = self.call_lpsolve_api(cust_adjust_position)
            
            # 输出测试结果
            print("\n" + "=" * 60)
            print("🎯 测试结果总结")
            print("=" * 60)
            
            if "error" not in result:
                print("✅ 功能测试通过")
                if result.get('code') == 0:
                    print("✅ API返回成功")
                    if result.get('data'):
                        print("✅ 获得调仓方案")
                    else:
                        print("⚠️  无调仓方案（可能无解）")
                else:
                    print(f"⚠️  API返回错误: {result.get('message')}")
            else:
                print("❌ 功能测试失败")
                print(f"错误: {result.get('message')}")
                
        except Exception as e:
            print(f"\n❌ 测试过程中发生异常: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    # 运行功能测试
    test = TestRetailAdjustAPI()
    test.run_test()
