# 1. 数据模型

继承的父类： pydantic.BaseModel

## 1.1. 推荐产品池

    类名： RecommendProductPool
    
    字段：
        <!-- 基金代码 -->
        product_code: str
        <!-- 产品类型，筛选优先级：国内大类、全球配置表、其他 -->
        product_priority: str
        <!-- 国内股票占比 -->
        stocks_domestic_proportion: float
        <!-- 海外股票占比 -->
        stocks_overseas_proportion: float
        <!-- 国内债券占比 -->
        bonds_domestic_proportion: float
        <!-- 海外债券占比 -->
        bonds_overseas_proportion: float
        <!-- 商品占比 -->
        commodity_proportion: float
        <!-- 策略类型，单策略、双拼策略、多策略 -->
        strategy_type: str
        <!-- 单笔购买金额下限 -->
        lower_amount_limit: float
        <!-- 单笔购买金额上限 -->
        upper_amount_limit: float

## 1.2. 客户当前持仓信息

    类名：CustPositionHolding

    <!-- 客户一账通号 -->
    hbone_no: str
    <!-- 国内债券持有占比 -->
    holding_bonds_domestic_proportion: float
    <!-- 海外债券持有占比 -->
    holding_bonds_overseas_proportion: float
    <!-- 持有现金金额，国强接口：additionalMoney -->
    holding_cash_amount: float
    <!-- 商品持有占比 -->
    holding_commodity_proportion: float
    <!-- 持仓市值，与国强团队待确认 -->
    holding_market_cap: float
    <!-- 国内股票持有占比 -->
    holding_stocks_domestic_proportion: float
    <!-- 海外股票持有占比 -->
    holding_stocks_overseas_proportion: float
    <!-- 国内债券推荐占比 -->
    recommend_bonds_domestic_proportion: float
    <!-- 海外债券推荐占比 -->
    recommend_bonds_overseas_proportion: float
    <!-- 商品推荐占比 -->
    recommend_commodity_proportion: float
    <!-- 国内股票推荐占比 -->
    recommend_stocks_domestic_proportion: float
    <!-- 海外股票推荐占比 -->
    recommend_stocks_overseas_proportion: float

## 1.3. 客户调仓

    类名：CustAdjustPosition

    <!-- 请求id -->
    request_id: str
    <!-- 客户当前持仓信息 -->
    cust_position_holding: CustPositionHolding
    <!-- 推荐产品池 -->
    recommend_product_pool: List[RecommendProductPool]


## 1.4 优化调仓信息
    类名：AdjustProductInfo

    <!-- 产品代码 -->
    product_code: str
    <!-- 调仓金额 -->
    product_type: float
    <!-- 调仓金额 -->
    adjust_amount: float
    



## 1.5 客户线性优化调仓结果

    类名：CustLpOptimalSolution

    <!-- 客户一账通号 -->
    hbone_no: str
    <!-- 基金数量 -->
    fund_num: int
    <!-- 策略优先级最小值 -->
    strategy_priority_min: int
    <!-- 策略优先级求和 -->
    strategy_priority_sum: int
    <!-- 产品优先级最小值 -->
    product_priority_min: int
    <!-- 产品优先级求和 -->
    product_priority_sum: int
    <!-- 偏离度均值 -->
    deviation_avg: float
    <!-- 调仓方案 -->
    adjust_solution: List[AdjustProductInfo]


# 2. 功能测试
(1) 读取src\tets\cust_hold.csv文件，生成CustPositionHolding对象;
(2) 读取src\tets\recommend_product_pool.csv文件，生成RecommendProductPool对象;
(3) 根据步骤2中生成的RecommendProductPool对象, 当前仅每次取1个product_code，生成投资组合列表 portfolio_list: List[List[str]]
(4) CustPositionHolding, RecommendProductPool, portfolio_list 生成CustAdjustPosition对象, request_id 使用uuid随机生成
(5) post请求调用接口http://127.0.0.1:8080/api/v1/retail/adjust/lpsolve, post参数为CustAdjustPosition对象生成的json字符串





