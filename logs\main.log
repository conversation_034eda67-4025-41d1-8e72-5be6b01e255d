2025-08-29 09:11:54.214 |  | INFO | process [49204]:26784 | core.app:lifespan:17 - ProcessPoolExecutor initialized.
2025-08-29 09:11:54.214 |  | INFO | process [49204]:26784 | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-08-29 09:11:54.216 |  | INFO | process [49204]:26784 | uvicorn.server:_log_started_message:215 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-29 09:16:01.560 |  | INFO | process [49204]:26784 | uvicorn.server:shutdown:263 - Shutting down
2025-08-29 09:16:01.662 |  | INFO | process [49204]:26784 | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-08-29 09:16:01.663 |  | INFO | process [49204]:26784 | core.app:lifespan:21 - ProcessPoolExecutor shutdown.
2025-08-29 09:16:01.663 |  | INFO | process [49204]:26784 | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-08-29 09:16:01.663 |  | INFO | process [49204]:26784 | uvicorn.server:_serve:93 - Finished server process [49204]
2025-08-29 09:16:19.722 |  | INFO | process [25532]:47980 | core.app:lifespan:17 - ProcessPoolExecutor initialized.
2025-08-29 09:16:19.723 |  | INFO | process [25532]:47980 | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-08-29 09:16:19.725 |  | INFO | process [25532]:47980 | uvicorn.server:_log_started_message:215 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-29 09:16:51.732 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [25532]:47980 | api.v1.endpoints.retail:retail_adjust_lpsolve:15 - 请求参数: {'request_id': '5344e7de-6010-4c20-a8ad-b281ae0c3fab', 'cust_position_holding': {'hbone_no': '9202147754', 'holding_market_cap': 27722.41, 'holding_cash_amount': 18160.950791, 'holding_cash_proportion': 0.6551, 'holding_bonds_domestic_proportion': 0.0, 'holding_bonds_overseas_proportion': 0.0, 'holding_stocks_domestic_proportion': 0.3, 'holding_stocks_overseas_proportion': 0.0449, 'holding_commodity_proportion': 0.0, 'recommend_bonds_domestic_proportion': 0.45, 'recommend_bonds_overseas_proportion': 0.05, 'recommend_stocks_domestic_proportion': 0.3, 'recommend_stocks_overseas_proportion': 0.15, 'recommend_commodity_proportion': 0.05}, 'recommend_product_pool': [{'product_code': 'tzzhqgx', 'product_type': 'zh', 'stocks_domestic_proportion': 0.989, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.011, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'jszh013', 'product_type': 'zh', 'stocks_domestic_proportion': 0.981, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.0189, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0001, 'product_priority': '2', 'strategy_type': 'singleStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'tzzhqzx', 'product_type': 'zh', 'stocks_domestic_proportion': 0.0, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 1.0, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': '000217', 'product_type': 'fund', 'stocks_domestic_proportion': 0.0, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.0, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 1.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 0.1, 'upper_amount_limit': 99999999999.0}, {'product_code': '050030', 'product_type': 'fund', 'stocks_domestic_proportion': 0.0, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.0, 'bonds_overseas_proportion': 1.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 1.0, 'upper_amount_limit': 10000.0}, {'product_code': '017436', 'product_type': 'fund', 'stocks_domestic_proportion': 0.0, 'stocks_overseas_proportion': 1.0, 'bonds_domestic_proportion': 0.0, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 1.0, 'upper_amount_limit': 1000.0}, {'product_code': 'tzzhphx', 'product_type': 'zh', 'stocks_domestic_proportion': 0.4377, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.5623, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'doubleStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'GJ003', 'product_type': 'zh', 'stocks_domestic_proportion': 0.0774, 'stocks_overseas_proportion': 0.0314, 'bonds_domestic_proportion': 0.7316, 'bonds_overseas_proportion': 0.1001, 'commodity_proportion': 0.0595, 'product_priority': '1', 'strategy_type': 'multiStra', 'lower_amount_limit': 1000.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'GJ009', 'product_type': 'zh', 'stocks_domestic_proportion': 0.3356, 'stocks_overseas_proportion': 0.1233, 'bonds_domestic_proportion': 0.3548, 'bonds_overseas_proportion': 0.1131, 'commodity_proportion': 0.0732, 'product_priority': '1', 'strategy_type': 'multiStra', 'lower_amount_limit': 1000.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'GJ018', 'product_type': 'zh', 'stocks_domestic_proportion': 0.5327, 'stocks_overseas_proportion': 0.1982, 'bonds_domestic_proportion': 0.1078, 'bonds_overseas_proportion': 0.0907, 'commodity_proportion': 0.0706, 'product_priority': '1', 'strategy_type': 'multiStra', 'lower_amount_limit': 1000.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'jszh011', 'product_type': 'zh', 'stocks_domestic_proportion': 0.4962, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.3947, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.1091, 'product_priority': '2', 'strategy_type': 'multiStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'jszh012', 'product_type': 'zh', 'stocks_domestic_proportion': 0.7255, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.1663, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.1082, 'product_priority': '2', 'strategy_type': 'multiStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}], 'portfolio_list': [['GJ003', '017436']]}, lp问题数: 1
2025-08-29 09:16:51.734 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [25532]:47980 | services.retail_fund_adjust:_params_preprocessing:63 - 数据预处理....
2025-08-29 09:16:51.779 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [25532]:47980 | services.retail_fund_adjust:_params_preprocessing:73 - 数据预处理完成, 耗时0.044586896896362305秒
2025-08-29 09:16:51.780 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [25532]:47980 | services.retail_fund_adjust:multi_process_lp_solve:288 - 多进程求解LP问题开始...
2025-08-29 09:16:51.781 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [25532]:47980 | services.retail_fund_adjust:multi_process_lp_solve:290 - 多进程求解，任务数=1
2025-08-29 09:16:54.714 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [25532]:47980 | services.retail_fund_adjust:multi_process_lp_solve:293 - 多进程求解LP问题结束，有解数=1
2025-08-29 09:41:38.248 |  | INFO | process [44256]:30464 | core.app:lifespan:17 - ProcessPoolExecutor initialized.
2025-08-29 09:41:38.248 |  | INFO | process [44256]:30464 | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-08-29 09:41:38.250 |  | INFO | process [44256]:30464 | uvicorn.server:_log_started_message:215 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-29 09:41:49.050 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [44256]:30464 | api.v1.endpoints.retail:retail_adjust_lpsolve:15 - 请求参数: {'request_id': '5344e7de-6010-4c20-a8ad-b281ae0c3fab', 'cust_position_holding': {'hbone_no': '9202147754', 'holding_market_cap': 27722.41, 'holding_cash_amount': 18160.950791, 'holding_cash_proportion': 0.6551, 'holding_bonds_domestic_proportion': 0.0, 'holding_bonds_overseas_proportion': 0.0, 'holding_stocks_domestic_proportion': 0.3, 'holding_stocks_overseas_proportion': 0.0449, 'holding_commodity_proportion': 0.0, 'recommend_bonds_domestic_proportion': 0.45, 'recommend_bonds_overseas_proportion': 0.05, 'recommend_stocks_domestic_proportion': 0.3, 'recommend_stocks_overseas_proportion': 0.15, 'recommend_commodity_proportion': 0.05}, 'recommend_product_pool': [{'product_code': 'tzzhqgx', 'product_type': 'zh', 'stocks_domestic_proportion': 0.989, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.011, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'jszh013', 'product_type': 'zh', 'stocks_domestic_proportion': 0.981, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.0189, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0001, 'product_priority': '2', 'strategy_type': 'singleStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'tzzhqzx', 'product_type': 'zh', 'stocks_domestic_proportion': 0.0, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 1.0, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': '000217', 'product_type': 'fund', 'stocks_domestic_proportion': 0.0, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.0, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 1.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 0.1, 'upper_amount_limit': 99999999999.0}, {'product_code': '050030', 'product_type': 'fund', 'stocks_domestic_proportion': 0.0, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.0, 'bonds_overseas_proportion': 1.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 1.0, 'upper_amount_limit': 10000.0}, {'product_code': '017436', 'product_type': 'fund', 'stocks_domestic_proportion': 0.0, 'stocks_overseas_proportion': 1.0, 'bonds_domestic_proportion': 0.0, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 1.0, 'upper_amount_limit': 1000.0}, {'product_code': 'tzzhphx', 'product_type': 'zh', 'stocks_domestic_proportion': 0.4377, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.5623, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'doubleStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'GJ003', 'product_type': 'zh', 'stocks_domestic_proportion': 0.0774, 'stocks_overseas_proportion': 0.0314, 'bonds_domestic_proportion': 0.7316, 'bonds_overseas_proportion': 0.1001, 'commodity_proportion': 0.0595, 'product_priority': '1', 'strategy_type': 'multiStra', 'lower_amount_limit': 1000.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'GJ009', 'product_type': 'zh', 'stocks_domestic_proportion': 0.3356, 'stocks_overseas_proportion': 0.1233, 'bonds_domestic_proportion': 0.3548, 'bonds_overseas_proportion': 0.1131, 'commodity_proportion': 0.0732, 'product_priority': '1', 'strategy_type': 'multiStra', 'lower_amount_limit': 1000.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'GJ018', 'product_type': 'zh', 'stocks_domestic_proportion': 0.5327, 'stocks_overseas_proportion': 0.1982, 'bonds_domestic_proportion': 0.1078, 'bonds_overseas_proportion': 0.0907, 'commodity_proportion': 0.0706, 'product_priority': '1', 'strategy_type': 'multiStra', 'lower_amount_limit': 1000.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'jszh011', 'product_type': 'zh', 'stocks_domestic_proportion': 0.4962, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.3947, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.1091, 'product_priority': '2', 'strategy_type': 'multiStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'jszh012', 'product_type': 'zh', 'stocks_domestic_proportion': 0.7255, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.1663, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.1082, 'product_priority': '2', 'strategy_type': 'multiStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}], 'portfolio_list': [['GJ003', '017436']]}, lp问题数: 1
2025-08-29 09:41:49.064 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [44256]:30464 | services.retail_fund_adjust:_params_preprocessing:63 - 数据预处理....
2025-08-29 09:41:49.072 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [44256]:30464 | services.retail_fund_adjust:_params_preprocessing:73 - 数据预处理完成, 耗时0.009411334991455078秒
2025-08-29 09:41:49.075 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [44256]:30464 | services.retail_fund_adjust:multi_process_lp_solve:288 - 多进程求解LP问题开始...
2025-08-29 09:41:49.076 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [44256]:30464 | services.retail_fund_adjust:multi_process_lp_solve:290 - 多进程求解，任务数=1
2025-08-29 09:41:50.429 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [44256]:30464 | services.retail_fund_adjust:multi_process_lp_solve:293 - 多进程求解LP问题结束，有解数=1
2025-08-29 09:41:50.504 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [44256]:30464 | services.retail_fund_adjust:multi_process_lp_solve:299 - 按优先级排序选择最终方案完成，输出结果返回：{'hbone_no': '9202147754', 'fund_num': 2, 'strategy_priority_min': 1, 'strategy_priority_sum': 4, 'product_priority_min': 1, 'product_priority_sum': 2, 'deviation_avg': 0.10213424411874725, 'adjust_solution': [{'product_code': '017436', 'adjust_amount': 1000.0, 'product_type': 'fund'}, {'product_code': 'GJ003', 'adjust_amount': 17160.951, 'product_type': 'zh'}]}
2025-08-29 09:41:50.505 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [44256]:30464 | api.v1.endpoints.retail:retail_adjust_lpsolve:35 - 总耗时1.4552192687988281秒
2025-08-29 09:49:45.193 |  | INFO | process [41524]:46688 | core.app:lifespan:17 - ProcessPoolExecutor initialized.
2025-08-29 09:49:45.195 |  | INFO | process [41524]:46688 | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-08-29 09:49:45.197 |  | ERROR | process [41524]:46688 | uvicorn.server:startup:171 - [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8080): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-08-29 09:49:45.198 |  | INFO | process [41524]:46688 | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-08-29 09:49:45.199 |  | INFO | process [41524]:46688 | core.app:lifespan:21 - ProcessPoolExecutor shutdown.
2025-08-29 09:49:45.200 |  | INFO | process [41524]:46688 | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-08-29 09:50:01.049 |  | INFO | process [46624]:47416 | core.app:lifespan:17 - ProcessPoolExecutor initialized.
2025-08-29 09:50:01.050 |  | INFO | process [46624]:47416 | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-08-29 09:50:01.051 |  | ERROR | process [46624]:47416 | uvicorn.server:startup:171 - [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8080): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-08-29 09:50:01.052 |  | INFO | process [46624]:47416 | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-08-29 09:50:01.053 |  | INFO | process [46624]:47416 | core.app:lifespan:21 - ProcessPoolExecutor shutdown.
2025-08-29 09:50:01.054 |  | INFO | process [46624]:47416 | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-08-29 09:50:18.621 |  | INFO | process [44256]:30464 | uvicorn.server:shutdown:263 - Shutting down
2025-08-29 09:50:18.730 |  | INFO | process [44256]:30464 | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-08-29 09:50:18.732 |  | INFO | process [44256]:30464 | core.app:lifespan:21 - ProcessPoolExecutor shutdown.
2025-08-29 09:50:18.733 |  | INFO | process [44256]:30464 | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-08-29 09:50:18.733 |  | INFO | process [44256]:30464 | uvicorn.server:_serve:93 - Finished server process [44256]
2025-08-29 09:50:28.621 |  | INFO | process [48644]:45012 | core.app:lifespan:17 - ProcessPoolExecutor initialized.
2025-08-29 09:50:28.623 |  | INFO | process [48644]:45012 | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-08-29 09:50:28.627 |  | INFO | process [48644]:45012 | uvicorn.server:_log_started_message:215 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-29 09:51:05.890 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [48644]:45012 | api.v1.endpoints.retail:retail_adjust_lpsolve:15 - 请求参数: {'request_id': '5344e7de-6010-4c20-a8ad-b281ae0c3fab', 'cust_position_holding': {'hbone_no': '9202147754', 'holding_market_cap': 27722.41, 'holding_cash_amount': 18160.950791, 'holding_cash_proportion': 0.6551, 'holding_bonds_domestic_proportion': 0.0, 'holding_bonds_overseas_proportion': 0.0, 'holding_stocks_domestic_proportion': 0.3, 'holding_stocks_overseas_proportion': 0.0449, 'holding_commodity_proportion': 0.0, 'recommend_bonds_domestic_proportion': 0.45, 'recommend_bonds_overseas_proportion': 0.05, 'recommend_stocks_domestic_proportion': 0.3, 'recommend_stocks_overseas_proportion': 0.15, 'recommend_commodity_proportion': 0.05}, 'recommend_product_pool': [{'product_code': 'tzzhqgx', 'product_type': 'zh', 'stocks_domestic_proportion': 0.989, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.011, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'jszh013', 'product_type': 'zh', 'stocks_domestic_proportion': 0.981, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.0189, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0001, 'product_priority': '2', 'strategy_type': 'singleStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'tzzhqzx', 'product_type': 'zh', 'stocks_domestic_proportion': 0.0, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 1.0, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': '000217', 'product_type': 'fund', 'stocks_domestic_proportion': 0.0, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.0, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 1.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 0.1, 'upper_amount_limit': 99999999999.0}, {'product_code': '050030', 'product_type': 'fund', 'stocks_domestic_proportion': 0.0, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.0, 'bonds_overseas_proportion': 1.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 1.0, 'upper_amount_limit': 10000.0}, {'product_code': '017436', 'product_type': 'fund', 'stocks_domestic_proportion': 0.0, 'stocks_overseas_proportion': 1.0, 'bonds_domestic_proportion': 0.0, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 1.0, 'upper_amount_limit': 1000.0}, {'product_code': 'tzzhphx', 'product_type': 'zh', 'stocks_domestic_proportion': 0.4377, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.5623, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'doubleStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'GJ003', 'product_type': 'zh', 'stocks_domestic_proportion': 0.0774, 'stocks_overseas_proportion': 0.0314, 'bonds_domestic_proportion': 0.7316, 'bonds_overseas_proportion': 0.1001, 'commodity_proportion': 0.0595, 'product_priority': '1', 'strategy_type': 'multiStra', 'lower_amount_limit': 1000.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'GJ009', 'product_type': 'zh', 'stocks_domestic_proportion': 0.3356, 'stocks_overseas_proportion': 0.1233, 'bonds_domestic_proportion': 0.3548, 'bonds_overseas_proportion': 0.1131, 'commodity_proportion': 0.0732, 'product_priority': '1', 'strategy_type': 'multiStra', 'lower_amount_limit': 1000.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'GJ018', 'product_type': 'zh', 'stocks_domestic_proportion': 0.5327, 'stocks_overseas_proportion': 0.1982, 'bonds_domestic_proportion': 0.1078, 'bonds_overseas_proportion': 0.0907, 'commodity_proportion': 0.0706, 'product_priority': '1', 'strategy_type': 'multiStra', 'lower_amount_limit': 1000.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'jszh011', 'product_type': 'zh', 'stocks_domestic_proportion': 0.4962, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.3947, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.1091, 'product_priority': '2', 'strategy_type': 'multiStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'jszh012', 'product_type': 'zh', 'stocks_domestic_proportion': 0.7255, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.1663, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.1082, 'product_priority': '2', 'strategy_type': 'multiStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}], 'portfolio_list': [['GJ003', '017436']]}, lp问题数: 1
2025-08-29 09:51:05.891 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [48644]:45012 | services.retail_fund_adjust:_params_preprocessing:63 - 数据预处理....
2025-08-29 09:51:05.911 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [48644]:45012 | services.retail_fund_adjust:_params_preprocessing:73 - 数据预处理完成, 耗时0.019545793533325195秒
2025-08-29 09:51:05.912 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [48644]:45012 | services.retail_fund_adjust:multi_process_lp_solve:288 - 多进程求解LP问题开始...
2025-08-29 09:51:05.915 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [48644]:45012 | services.retail_fund_adjust:multi_process_lp_solve:290 - 多进程求解，任务数=1
2025-08-29 09:51:08.821 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [48644]:45012 | services.retail_fund_adjust:multi_process_lp_solve:293 - 多进程求解LP问题结束，有解数=1
2025-08-29 09:52:36.891 |  | INFO | process [45440]:39856 | core.app:lifespan:17 - ProcessPoolExecutor initialized.
2025-08-29 09:52:36.892 |  | INFO | process [45440]:39856 | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-08-29 09:52:36.897 |  | INFO | process [45440]:39856 | uvicorn.server:_log_started_message:215 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-29 09:54:38.156 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [45440]:39856 | api.v1.endpoints.retail:retail_adjust_lpsolve:15 - 请求参数: {'request_id': '5344e7de-6010-4c20-a8ad-b281ae0c3fab', 'cust_position_holding': {'hbone_no': '9202147754', 'holding_market_cap': 27722.41, 'holding_cash_amount': 18160.950791, 'holding_cash_proportion': 0.6551, 'holding_bonds_domestic_proportion': 0.0, 'holding_bonds_overseas_proportion': 0.0, 'holding_stocks_domestic_proportion': 0.3, 'holding_stocks_overseas_proportion': 0.0449, 'holding_commodity_proportion': 0.0, 'recommend_bonds_domestic_proportion': 0.45, 'recommend_bonds_overseas_proportion': 0.05, 'recommend_stocks_domestic_proportion': 0.3, 'recommend_stocks_overseas_proportion': 0.15, 'recommend_commodity_proportion': 0.05}, 'recommend_product_pool': [{'product_code': 'tzzhqgx', 'product_type': 'zh', 'stocks_domestic_proportion': 0.989, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.011, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'jszh013', 'product_type': 'zh', 'stocks_domestic_proportion': 0.981, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.0189, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0001, 'product_priority': '2', 'strategy_type': 'singleStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'tzzhqzx', 'product_type': 'zh', 'stocks_domestic_proportion': 0.0, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 1.0, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': '000217', 'product_type': 'fund', 'stocks_domestic_proportion': 0.0, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.0, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 1.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 0.1, 'upper_amount_limit': 99999999999.0}, {'product_code': '050030', 'product_type': 'fund', 'stocks_domestic_proportion': 0.0, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.0, 'bonds_overseas_proportion': 1.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 1.0, 'upper_amount_limit': 10000.0}, {'product_code': '017436', 'product_type': 'fund', 'stocks_domestic_proportion': 0.0, 'stocks_overseas_proportion': 1.0, 'bonds_domestic_proportion': 0.0, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'singleStra', 'lower_amount_limit': 1.0, 'upper_amount_limit': 1000.0}, {'product_code': 'tzzhphx', 'product_type': 'zh', 'stocks_domestic_proportion': 0.4377, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.5623, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.0, 'product_priority': '1', 'strategy_type': 'doubleStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'GJ003', 'product_type': 'zh', 'stocks_domestic_proportion': 0.0774, 'stocks_overseas_proportion': 0.0314, 'bonds_domestic_proportion': 0.7316, 'bonds_overseas_proportion': 0.1001, 'commodity_proportion': 0.0595, 'product_priority': '1', 'strategy_type': 'multiStra', 'lower_amount_limit': 1000.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'GJ009', 'product_type': 'zh', 'stocks_domestic_proportion': 0.3356, 'stocks_overseas_proportion': 0.1233, 'bonds_domestic_proportion': 0.3548, 'bonds_overseas_proportion': 0.1131, 'commodity_proportion': 0.0732, 'product_priority': '1', 'strategy_type': 'multiStra', 'lower_amount_limit': 1000.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'GJ018', 'product_type': 'zh', 'stocks_domestic_proportion': 0.5327, 'stocks_overseas_proportion': 0.1982, 'bonds_domestic_proportion': 0.1078, 'bonds_overseas_proportion': 0.0907, 'commodity_proportion': 0.0706, 'product_priority': '1', 'strategy_type': 'multiStra', 'lower_amount_limit': 1000.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'jszh011', 'product_type': 'zh', 'stocks_domestic_proportion': 0.4962, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.3947, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.1091, 'product_priority': '2', 'strategy_type': 'multiStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}, {'product_code': 'jszh012', 'product_type': 'zh', 'stocks_domestic_proportion': 0.7255, 'stocks_overseas_proportion': 0.0, 'bonds_domestic_proportion': 0.1663, 'bonds_overseas_proportion': 0.0, 'commodity_proportion': 0.1082, 'product_priority': '2', 'strategy_type': 'multiStra', 'lower_amount_limit': 500.0, 'upper_amount_limit': 999999999.0}], 'portfolio_list': [['GJ003', '017436']]}, lp问题数: 1
2025-08-29 09:54:38.158 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [45440]:39856 | services.retail_fund_adjust:_params_preprocessing:63 - 数据预处理....
2025-08-29 09:54:38.178 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [45440]:39856 | services.retail_fund_adjust:_params_preprocessing:73 - 数据预处理完成, 耗时0.019854307174682617秒
2025-08-29 09:54:38.179 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [45440]:39856 | services.retail_fund_adjust:multi_process_lp_solve:288 - 多进程求解LP问题开始...
2025-08-29 09:54:38.180 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [45440]:39856 | services.retail_fund_adjust:multi_process_lp_solve:290 - 多进程求解，任务数=1
2025-08-29 09:54:42.893 | 5344e7de-6010-4c20-a8ad-b281ae0c3fab | INFO | process [45440]:39856 | services.retail_fund_adjust:multi_process_lp_solve:293 - 多进程求解LP问题结束，有解数=1
