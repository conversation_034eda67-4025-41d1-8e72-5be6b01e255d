import pandas as pd
from loguru import logger as base_logger
from typing import List, Set, Dict
import pulp
import numpy as np
from pathlib import Path
from models.fof import FofPortfolio, CustHoldRatio, CustRecommendRatio, FofLpProblem, _FIELD_GROUPS
from core import basic_config
from models.fof import CustOptimizeRatio
from core.app import run_in_process_pool
import asyncio
import time

log_dir = basic_config.LOGGING_BASE_DIR

# 创建专用于fof服务的logger实例
logger = base_logger.bind(service="fof")
logger.configure(extra={"request_id": ''})  # Default values 否则会报错

log_format = (
    # 时间信息 + UUID
    "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | {extra[request_id]} | "
    # 日志级别，居中对齐
    "<level>{level: ^4}</level> | "
    # 进程和线程信息
    "process [<cyan>{process}</cyan>]:<cyan>{thread}</cyan> | "
    # 文件、函数和行号
    "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
    # 日志消息
    "<level>{message}</level>"
)

# 确保fof日志目录存在
fof_log_dir = Path(log_dir, "fof")
fof_log_dir.mkdir(parents=True, exist_ok=True)

# 添加fof专用的文件日志处理器（不移除现有处理器，保持控制台输出）
logger.add(
        str(Path(fof_log_dir, "fof_lp_solve.log")),
        format=log_format,
        level="INFO",
        rotation="10 MB",
        retention="365 days",
        encoding="utf-8",
        enqueue=True,  # 异步写入
        filter=lambda record: record.get("extra", {}).get("service") == "fof"
    )


class FofOptimizeService:

    def __init__(self, request_id: str, hbone_no: str, cust_optimize_ratio: CustOptimizeRatio):
        """
        :param request_id: 请求ID
        :param hbone_no: 客户号
        :param cust_optimize_ratio: 客户持仓优化占比（当前占比、推荐占比、调仓产品池）
        """
        self._request_id = request_id  # 请求ID
        self._hbone_no = hbone_no # 客户号

        # 参数预处理,转换成pandas DafaFrame
        prep_params = self._params_preprocessing(cust_optimize_ratio)
        self._fof_portfolio = prep_params["fof_portfolio"]  # 产品池
        self._cust_hold_ratio = prep_params["cust_hold_ratio"] # 客户持仓比例
        self._cust_recommend_ratio = prep_params["cust_recommend_ratio"] # 客户推荐比例
        self._lp_problem_list = prep_params["lp_problem_list"]  # 求解问题列表
        self._in_strategy = prep_params["in_strategy"] # 需要加仓的策略


    def _params_preprocessing(self, cust_optimize_ratio: CustOptimizeRatio):
        """
        :param cust_optimize_ratio: 客户持仓优化占比（当前占比、推荐占比、调仓产品池）
        :return: 预处理后的参数
        """

        start_time = time.time()

        with logger.contextualize(request_id=self._request_id):
            logger.info(f"数据预处理....")
            
            fof_portfolio = cust_optimize_ratio.fof_portfolio
            cust_hold_ratio = cust_optimize_ratio.cust_hold_ratio
            cust_recommend_ratio = cust_optimize_ratio.cust_recommend_ratio
            lp_problem_list = cust_optimize_ratio.lp_problem_list
            
            # 数据转为DataFrame类型
            df_fof_portfolio = pd.DataFrame(data=fof_portfolio.data, columns=fof_portfolio.columns).set_index("index")
            df_cust_hold_ratio = pd.DataFrame(data=[cust_hold_ratio.data], columns=cust_hold_ratio.columns)
            df_cust_recommend_ratio = pd.DataFrame(data=[cust_recommend_ratio.data], columns=cust_recommend_ratio.columns)

            # 预设的所有可能策略类型
            all_strategy = set()
            for group_name in _FIELD_GROUPS.keys():
                all_strategy.add(group_name)
                all_strategy |= _FIELD_GROUPS[group_name]

            # 动态调整的策略类型
            in_strategy = set(fof_portfolio.columns) & all_strategy

            # 数据类型
            in_strategy_dtype = dict()
            for col in in_strategy:
                in_strategy_dtype[col] = "float"

            df_fof_portfolio[list(in_strategy)] = df_fof_portfolio[list(in_strategy)].astype(in_strategy_dtype)
            df_cust_hold_ratio[list(in_strategy)] = df_cust_hold_ratio[list(in_strategy)].astype(in_strategy_dtype)
            df_cust_recommend_ratio[list(in_strategy)] = df_cust_recommend_ratio[list(in_strategy)].astype(in_strategy_dtype)
            df_fof_portfolio["min_amt"] = df_fof_portfolio["min_amt"].astype(np.float64)
            df_cust_hold_ratio["market_cap"] = df_cust_hold_ratio["market_cap"].astype(np.float64)

            logger.info(f"数据预处理完成, 耗时{time.time() - start_time}秒")

            return {"fof_portfolio": df_fof_portfolio, "cust_hold_ratio": df_cust_hold_ratio, "cust_recommend_ratio": df_cust_recommend_ratio, 
                    "lp_problem_list": lp_problem_list, "in_strategy": in_strategy}
           

    def fof_lp_solve(self, lp_problem: FofLpProblem):
        """
        线性规划求解
        :param lp_problem: 规划问题
        :return:
        """

        start_time = time.time()

        with logger.contextualize(request_id=self._request_id):
            
            # logger.info("开始求解线性规划")

            # 产品组合， 通过index进行查找
            df_product_comb = self._fof_portfolio.loc[lp_problem.fof_portfolio_index, :]
            # logger.info(f"<R>----{df_product_comb}")
            
            # 重设索引
            df_product_comb.reset_index(inplace=True, drop=True)
            # logger.info(f"<N>----{df_product_comb}")

            # 产品列表
            product_names = df_product_comb["fund_code"].to_list()
            # logger.info(product_names)

            # 产品数量
            num_products = len(product_names)

            # 偏离度
            bias = lp_problem.bias

            # (1) 定义一个规划问题
            '''
            pulp.LpProblem 是定义问题的构造函数。
            "MyMILPProblem" 是定义的问题名。
            参数 sense 用来指定求最小值/最大值问题，可选参数值：LpMinimize、LpMaximize
            '''
            MyProbLP = pulp.LpProblem("MyMILPProblem", sense=pulp.LpMinimize)


            # (2) 定义决策变量
            '''
            pulp.LpVariable 是定义决策变量的函数。
            'x' 是用户定义的变量名。
            参数 lowBound、upBound 用来设定决策变量的下界、上界；可以不定义下界/上界，默认的下界/上界是负无穷/正无穷。
            参数 cat 用来设定变量类型，可选参数值：'Continuous' 表示连续变量（默认值）、'Integer' 表示离散变量（用于整数规划问题）、'Binary' 表示0/1变量（用于0/1规划问题）。
            '''

            # x 的单位是10万, 上限设置了10亿
            UNIT_COEF = 100000
            lp_var_dict = pulp.LpVariable.dicts("x", range(num_products), lowBound=1, upBound=10000, cat='Integer')
            # logger.info(f"<X>----{lp_var_dict}")

            # (3) 设置目标函数，购买产品的总金额最小
            '''
            添加目标函数使用 “问题名 += 目标函数式” 格式。
            '''
            MyProbLP += pulp.lpSum([lp_var_dict[i] for i in range(num_products)])

            # (4) 添加约束条件
            '''
            添加约束条件使用 “问题名 += 约束条件表达式” 格式。
            约束条件可以是等式约束或不等式约束，不等式约束可以是 小于等于 或 大于等于，分别使用关键字">="、"<=“和”=="。
            '''

            # 约束条件： 加仓金额要大于初始值，传入的最小加仓金额的单位约定为1元
            MIN_AMT_UNIT = 1
            for i in range(num_products):
                MyProbLP += (lp_var_dict[i]*UNIT_COEF >= int(df_product_comb.loc[i, "min_amt"])*MIN_AMT_UNIT)

            # 根据推荐配置、偏离度，设置约束条件
            cust_market_cap = float(self._cust_hold_ratio.loc[0, 'market_cap'])
            new_total_amt = (pulp.lpSum(lp_var_dict[i] * UNIT_COEF for i in range(num_products)) + cust_market_cap)

            for st in self._in_strategy:
                new_st_amt = pulp.lpSum(lp_var_dict[i] * df_product_comb.loc[i, st] * UNIT_COEF for i in range(num_products)) + cust_market_cap * self._cust_hold_ratio.loc[0, st]
                rec_st_amt_up = new_total_amt * self._cust_recommend_ratio.loc[0, st] * (1 + bias)
                rec_st_amt_low = new_total_amt * self._cust_recommend_ratio.loc[0, st] * (1 - bias)

                MyProbLP += (new_st_amt <= rec_st_amt_up)
                MyProbLP += (new_st_amt >= rec_st_amt_low)

            
            # (5) 求解
            MyProbLP.solve(pulp.PULP_CBC_CMD(msg=0))

            if pulp.LpStatus[MyProbLP.status] == 'Optimal':
                solve_result = {
                    'products': product_names,
                    'amounts': [v.varValue * UNIT_COEF for v in MyProbLP.variables()],
                    'bias': bias
                }
                
                cost_time = time.time() - start_time
                logger.info(f"求解问题=【{lp_problem}】，求解结果=【{solve_result}】，lp求解耗时{cost_time}秒")
                return solve_result

            else:
                cost_time = time.time() - start_time
                logger.info(f"求解问题=【{lp_problem}】，求解结果=【无解】，求解耗时{cost_time}秒")
                return None    
        
    # 返回加仓后的各个策略的新占比和偏离度之和
    def output_optimize_result(self, lp_result_list: List) -> pd.DataFrame:
        """
        :param lp_result_list: LP求解结果
        :return: 返回加仓后的各个策略的新占比和偏离度之和
        """

        df_lp_result = pd.DataFrame(lp_result_list)

        # 深拷贝，防止修改原始数据
        df_cust_hold_ratio = self._cust_hold_ratio.copy()
        df_cust_recommend_ratio = self._cust_recommend_ratio.copy()

        # 拆成多行
        df_lp_result_explode = df_lp_result.explode(['products', 'amounts'])
        # 将explode之前的index设置成comb_index，方便后面进行group by
        df_lp_result_explode['comb_index'] = df_lp_result_explode.index
        # 设置类型
        df_lp_result_explode['amounts'] = df_lp_result_explode['amounts'].astype(np.int64)
        
        # 匹配产品池
        df_prod_amt = pd.merge(df_lp_result_explode, self._fof_portfolio, left_on='products', right_on='fund_code').drop('fund_code', axis=1)
        df_prod_amt['fund_code_amt'] = df_prod_amt['products'] + ':' + df_prod_amt['amounts'].astype(str)

        # 计算加仓产品每个策略的金额
        for st in self._in_strategy:
            st_amt_col = f'{st}_amt'
            df_prod_amt[st_amt_col] = df_prod_amt[st] * df_prod_amt["amounts"]
        
        # 汇总
        df_prod_amt_new = df_prod_amt.groupby(by=['comb_index'], as_index=True).agg(
            fund_code_amt = ('fund_code_amt', lambda x: ';'.join(x)),
            amounts=('amounts', 'sum'),
            fund_num=('comb_index', 'count'),
            bias=('bias','mean')
        ).reset_index()

        for st in self._in_strategy:
            # 按策略类型汇总
            st_amt_col = f'{st}_amt'
            df_tmp = df_prod_amt.groupby(by=['comb_index'], as_index=True)[st_amt_col].sum().rename(st_amt_col).reset_index()
            df_prod_amt_new = pd.merge(df_prod_amt_new, df_tmp, on='comb_index')

            # 字段重命名
            st_cur_col = f'cur_{st}'
            df_cust_hold_ratio.rename(columns={st: st_cur_col}, inplace=True)
            st_rec_col = f'rec_{st}'
            df_cust_recommend_ratio.rename(columns={st: st_rec_col}, inplace=True)

        df_prod_amt_new['m_key'] = 1
        df_cust_hold_ratio['m_key'] = 1
        df_cust_recommend_ratio['m_key'] = 1

        df_result = df_prod_amt_new.merge(df_cust_hold_ratio, on='m_key').merge(df_cust_recommend_ratio, on='m_key').drop('m_key', axis=1)
        df_result["new_market_cap"] = df_result["market_cap"] + df_result["amounts"]
        df_result["deviation"] = 0
        df_result["hbone_no"] = self._hbone_no
        
        for st in self._in_strategy:
            st_new_col = f'new_{st}'
            st_cur_col = f'cur_{st}'
            st_rec_col = f'rec_{st}'
            st_amt_col = f'{st}_amt'
            st_deviation_col = f'{st}_deviation'

            # 计算新的策略占比
            df_result[st_new_col] = (df_result["market_cap"] * df_result[st_cur_col] + df_result[st_amt_col])/df_result["new_market_cap"]

            # 计算策略的偏离度绝对值比值
            df_result[st_deviation_col] = abs(df_result[st_new_col] - df_result[st_rec_col])/df_result[st_rec_col]

            # 策略的偏离度绝对值之和
            df_result["deviation"] = df_result["deviation"] + abs(df_result[st_new_col] - df_result[st_rec_col])


        '''
        output_column = ['hbone_no','bias','fund_num','fund_code_amt','amounts',
                    'gp_gn_amt','gp_hw_amt','cta_amt','gs_gn_amt','gs_hw_amt','ll_amt',
                    'new_market_cap','new_gp_gn','new_gp_hw','new_cta','new_gs_gn','new_gs_hw','new_ll',
                    'gp_gn_deviation','gp_hw_deviation','cta_deviation','gs_gn_deviation','gs_hw_deviation','ll_deviation',
                    'deviation']
        '''

        # 固定输出
        output_column = ['hbone_no','bias','fund_num','fund_code_amt','amounts', 'new_market_cap', 'deviation']

        # 策略字段会动态变化
        for st in self._in_strategy:
            st_new_col = f'new_{st}'
            st_amt_col = f'{st}_amt'
            st_deviation_col = f'{st}_deviation'
            output_column.append(st_new_col)
            output_column.append(st_amt_col)
            output_column.append(st_deviation_col)
        
        df_output = df_result[output_column]
        df_output.fillna(0, inplace=True)

        return df_output


    async def multi_process_fof_lp_solve(self):
        """
        多进程求解多个线性规划
        :return solve_result_list: 求解结果列表
        """
        with logger.contextualize(request_id=self._request_id):
            logger.info(f"多进程求解LP问题开始...")
            tasks = [run_in_process_pool(self.fof_lp_solve, lp_problem) for lp_problem in self._lp_problem_list]
            solve_result_list = await asyncio.gather(*tasks)
            solve_result_list = [result for result in solve_result_list if result is not None]
            logger.info(f"多进程求解LP问题结束，有解数={len(solve_result_list)}")

            if len(solve_result_list) == 0:
               output_records = []
            else:
                logger.info(f"计算新占比等....")
                df_output_result = self.output_optimize_result(solve_result_list)
            
                output_records = df_output_result.to_dict(orient='records')
                logger.info(f"计算客户新持仓占比完成，输出结果返回：{output_records}")
            
            return output_records
